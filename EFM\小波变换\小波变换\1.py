import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

######################################
#
#
#定义一个小波，小波的形式是f(x)=exp[-((x - b) / a)**2 / 2]*cos(v * ((x - b) / a))
#wavelet(1, 5, 0, 0, 10)
######################################
def wavelet(av, v, b, a, x):
    """
    计算给定参数的波形值。
    """
    return av * np.exp(-((x - b) / a)**2 / 2) * np.cos(v * ((x - b) / a))

#######################################
#小波变换
#
#######################################
def transformation(a, b, av, v, data_array):
    """
    根据给定参数和数据数组，计算特定的函数值。
    """
    result = 0
    for row in data_array:
        x, multiplier = row
        wavelet_value = wavelet(av, v, b, a, x)
        result += multiplier * wavelet_value
    return result

def load_data(file_path):
    """
    从文件加载数据到二维数组。
    """
    data_array = []
    with open(file_path, 'r') as file:
        lines = file.readlines()
        for i, line in enumerate(lines):
            value = float(line.strip())
            data_array.append([(i + 1), value])
    return data_array

# 替换为你的文件路径
file_path = './output1.txt'
data_array = load_data(file_path)

# 定义参数范围
b_values = np.linspace(0, 400000, 10)  #0到0.04秒之间生成10个等间距的点
a_values = np.linspace(50000, 500000, 10)


A, B = np.meshgrid(a_values, b_values)

# 计算每个(a, b)点对应的f值
Z = np.zeros(A.shape)

# print(A,B,Z)
# exit()
for i in range(A.shape[0]):
    for j in range(A.shape[1]):
        Z[i, j] = transformation(A[i, j], B[i, j], av=1, v=5, data_array=data_array)
        print(A[i, j], B[i, j],'\n')

# 绘制三维图
fig = plt.figure(figsize=(10, 8))
ax = fig.add_subplot(111, projection='3d')
ax.plot_surface(A, B, Z, cmap='viridis')

ax.set_xlabel('a Value')
ax.set_ylabel('b Value')
ax.set_zlabel('f(a, b)')
plt.title('3D plot of f(a, b)')
plt.show()