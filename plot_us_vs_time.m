% 读取电压数据
data_folder = 'data20';  % 设置数据文件夹名称

% 定义共用的阶跃线数据（只需修改这里，两个图同时更新）
step_time_points = [8 10 10];  % 阶跃时间点（毫秒）
step_voltage_values = [0.002 0.02 0.002];  % 对应的电压值

% 读取原始数据文件
data1 = importdata(['result_cwtnew/' data_folder '/us_vs_time.txt']);
% 读取平滑数据文件
data2 = importdata(['result_cwtnew/' data_folder '/us_vs_time_smoothed.txt']);

% 处理第一个数据文件（原始数据）
if isstruct(data1)
    t1 = data1.data(:,1);
    Us1 = data1.data(:,2);
else
    t1 = data1(:,1);
    Us1 = data1(:,2);
end

% 处理第二个数据文件（平滑数据）
if isstruct(data2)
    t2 = data2.data(:,1);
    Us2 = data2.data(:,2);
else
    t2 = data2(:,1);
    Us2 = data2(:,2);
end

% 定义中间部分数据范围（毫秒）
mid_start = 9;  % 中间部分开始时间（毫秒）
mid_end = 14;   % 中间部分结束时间（毫秒）

% 提取中间部分数据
mid_idx1 = (t1*1000 >= mid_start) & (t1*1000 <= mid_end);
mid_Us1 = Us1(mid_idx1);

mid_idx2 = (t2*1000 >= mid_start) & (t2*1000 <= mid_end);
mid_Us2 = Us2(mid_idx2);

% 计算中间部分的最大值和最小值
mid_min1 = min(mid_Us1);
mid_max1 = max(mid_Us1);
mid_min2 = min(mid_Us2);
mid_max2 = max(mid_Us2);

% 创建第一个图形窗口（原始数据）
figure('Position', [100 100 800 500]);

% 绘制原始电压随时间变化曲线
h1 = plot(t1*1000, Us1, 'b-', 'LineWidth', 1.5);  % 将时间转换为毫秒
hold on;  % 保持当前图形，以便添加新的线

% 创建阶跃线的数据点（使用共用数据）
t_step = [step_time_points max(t1*1000)];  % 时间点（毫秒）
v_step = [step_voltage_values step_voltage_values(end)];  % 对应的电压值
h2 = plot(t_step, v_step, 'r-', 'LineWidth', 1.5);  % 绘制红色阶跃线

% 添加标题和标签
title([data_folder '电压Us随时间的变化 (原始数据)'], 'FontSize', 14);
xlabel('时间 (ms)', 'FontSize', 12);
ylabel('Us (V)', 'FontSize', 12);
grid on;

% 添加图例
legend([h1, h2], {'计算电压', '实际电压'}, 'Location', 'northeast', 'FontSize', 11);

% 设置图形外观
set(gcf, 'Color', 'w');  % 设置背景为白色
set(gca, 'LineWidth', 1.2);  % 设置坐标轴线宽
set(gca, 'FontSize', 11);  % 设置刻度字体大小

% 优化显示范围
xlim([min(t1*1000) max(t1*1000)]);
% 根据中间部分数据调整y轴范围
padding = (mid_max1 - mid_min1) * 0.1;  % 添加10%的边距
y_min = max(0, mid_min1 - padding);
y_max = mid_max1 + padding;
ylim([y_min, y_max]);

% 使用MATLAB默认Y轴格式
set(gca, 'YTickLabelMode', 'auto');

% 输出原始数据的基本统计信息
fprintf('=== 原始数据统计信息 ===\n');
fprintf('数据点数: %d\n', length(t1));
fprintf('时间范围: %.2f ms 到 %.2f ms\n', min(t1*1000), max(t1*1000));
fprintf('电压范围: %.2e V 到 %.2e V\n', min(Us1), max(Us1));
fprintf('平均电压: %.2e V\n', mean(Us1));
fprintf('中间部分电压范围(%.1f-%.1f ms): %.2e V 到 %.2e V\n', mid_start, mid_end, mid_min1, mid_max1);

% 创建第二个图形窗口（平滑数据）
figure('Position', [950 100 800 500]);  % 位置稍微偏右，避免重叠

% 绘制平滑电压随时间变化曲线
h3 = plot(t2*1000, Us2, 'b-', 'LineWidth', 1.5);  % 将时间转换为毫秒，使用蓝色
hold on;  % 保持当前图形，以便添加新的线

% 创建阶跃线的数据点（使用共用数据）
t_step2 = [step_time_points max(t2*1000)];  % 时间点（毫秒）
v_step2 = [step_voltage_values step_voltage_values(end)];  % 对应的电压值
h4 = plot(t_step2, v_step2, 'r-', 'LineWidth', 1.5);  % 绘制红色阶跃线

% 添加标题和标签
title([data_folder '电压Us随时间的变化 (平滑数据)'], 'FontSize', 14);
xlabel('时间 (ms)', 'FontSize', 12);
ylabel('Us (V)', 'FontSize', 12);
grid on;

% 添加图例
legend([h3, h4], {'计算电压(平滑)', '实际电压'}, 'Location', 'northeast', 'FontSize', 11);

% 设置图形外观
set(gcf, 'Color', 'w');  % 设置背景为白色
set(gca, 'LineWidth', 1.2);  % 设置坐标轴线宽
set(gca, 'FontSize', 11);  % 设置刻度字体大小

% 优化显示范围
xlim([min(t2*1000) max(t2*1000)]);
% 使用与第一个窗口相同的y轴范围
ylim([y_min, y_max]);

% 使用MATLAB默认Y轴格式
set(gca, 'YTickLabelMode', 'auto');

% 输出平滑数据的基本统计信息
fprintf('\n=== 平滑数据统计信息 ===\n');
fprintf('数据点数: %d\n', length(t2));
fprintf('时间范围: %.2f ms 到 %.2f ms\n', min(t2*1000), max(t2*1000));
fprintf('电压范围: %.2e V 到 %.2e V\n', min(Us2), max(Us2));
fprintf('平均电压: %.2e V\n', mean(Us2));
fprintf('中间部分电压范围(%.1f-%.1f ms): %.2e V 到 %.2e V\n', mid_start, mid_end, mid_min2, mid_max2);

% 输出数据比较信息
fprintf('\n=== 数据比较 ===\n');
if length(t1) == length(t2)
    diff_mean = mean(abs(Us1 - Us2));
    fprintf('平均电压差异: %.2e V\n', diff_mean);
    fprintf('最大电压差异: %.2e V\n', max(abs(Us1 - Us2)));
else
    fprintf('注意：两个数据文件的数据点数不同\n');
end
