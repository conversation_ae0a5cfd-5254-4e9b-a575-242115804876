import numpy as np
import os

def calculate_us_dynamic(A, displacement, k_i, Q_i, epsilon_0, z_a_base, R, U_AC):
    """
    根据给定的振幅A和动态位移计算Us
    
    参数:
    A: 振幅数组
    displacement: 位移数组（小波变换的第二列数据）
    k_i: k_i参数
    Q_i: Q_i参数
    epsilon_0: ε₀ 真空介电常数
    z_a_base: z_a基础值
    R: R参数
    U_AC: U_AC参数
    
    返回:
    Us: 计算得到的Us值数组
    z_a_dynamic: 动态z_a值数组
    """
    # 动态z_a = 基础z_a + 位移
    z_a_dynamic = z_a_base + displacement
    
    denominator_term = (z_a_dynamic/R)**2 + z_a_dynamic/R
    C = k_i / (2 * np.pi * epsilon_0 * Q_i)
    Us = C * A * denominator_term / U_AC
    return Us, z_a_dynamic

def importdata(file_path, delimiter=None, skip_header=0):
    """
    读取数据文件
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    with open(file_path, 'r') as file:
        lines = file.readlines()
    
    header_lines = lines[:skip_header]
    data_lines = lines[skip_header:]
    
    first_data_line = data_lines[0].strip()
    is_header = False
    
    parts = first_data_line.split(delimiter) if delimiter else first_data_line.split()
    try:
        [float(p) for p in parts]
    except ValueError:
        is_header = True
    
    if is_header:
        data_lines = data_lines[1:]
        header_lines.append(first_data_line)
    
    data = []
    for line in data_lines:
        line = line.strip()
        if not line:
            continue
            
        parts = line.split(delimiter) if delimiter else line.split()
        try:
            values = [float(p) for p in parts]
            data.append(values)
        except ValueError as e:
            print(f"跳过无法解析的行: {line}, 错误: {e}")
    
    return np.array(data) if data else np.array([])

def detect_signal_period_fft(signal, dt):
    """
    使用FFT检测信号的主要频率和周期
    
    参数:
    signal: 信号数组
    dt: 采样时间间隔
    
    返回:
    period: 检测到的周期（秒）
    frequency: 检测到的频率（Hz）
    """
    # 使用FFT检测主要频率
    n = len(signal)
    frequencies = np.fft.fftfreq(n, dt)
    fft_signal = np.fft.fft(signal)
    power = np.abs(fft_signal)**2
    
    # 只考虑正频率
    pos_mask = frequencies > 0
    pos_frequencies = frequencies[pos_mask]
    pos_power = power[pos_mask]
    
    # 找到功率最大的频率
    max_power_idx = np.argmax(pos_power)
    dominant_freq = pos_frequencies[max_power_idx]
    period = 1.0 / dominant_freq
    
    return period, dominant_freq

def read_wavelet_data(file_path, time_column=0, signal_column=1, amplitude_column=2):
    """
    从小波变换文件读取时间、信号（位移）和振幅数据
    """
    try:
        data = importdata(file_path)
        
        if data.size == 0:
            print(f"警告: 文件 {file_path} 中没有有效数据")
            return np.array([]), np.array([]), np.array([])
            
        if data.shape[1] <= max(time_column, signal_column, amplitude_column):
            raise ValueError(f"数据列数不足: 需要列 {time_column}、{signal_column} 和 {amplitude_column}，但只有 {data.shape[1]} 列")
            
        times = data[:, time_column]
        signals = data[:, signal_column]  # 位移数据
        amplitudes = data[:, amplitude_column]  # 振幅数据
        
        return times, signals, amplitudes
        
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
        return np.array([]), np.array([]), np.array([])





if __name__ == "__main__":
    # 参数设置
    k_i = 7.86  # 悬臂刚度
    Q_i = 250.8  # 悬臂品质因子
    epsilon_0 = 8.854e-12  # 真空介电常数
    z_a_base = 150e-9  # 探针抬升基础高度            # 注意修改！！！
    R = 20e-9  # 探针针尖半径
    U_AC = 3  # 交流激励电压的幅值

    # 设置数据文件夹
    data_folder = "data14"
    
    # 位移数据列选择 (1表示第二列，2表示第三列)
    displacement_column_choice = 1  # 可选择1或2，控制读取位移文件的哪一列数据

    # 直接指定小波变换文件路径
    displacement_file = f"result_cwtanalyze_dynamic_all/{data_folder}/wavelet_75000Hz.txt"
    amplitude_file = f"result_cwtanalyze_dynamic_all/{data_folder}/wavelet_470285Hz.txt"
    
    # 设置频率用于显示
    displacement_frequency = 74999   # 位移信号来源频率：74999Hz
    amplitude_frequency = 469995     # 振幅数据来源频率：469995Hz
    
    # 检查文件是否存在
    missing_files = []
    if not os.path.exists(displacement_file):
        missing_files.append(displacement_file)
    if not os.path.exists(amplitude_file):
        missing_files.append(amplitude_file)
    
    if missing_files:
        print("未找到以下文件:")
        for file in missing_files:
            print(f"  {file}")
    else:
        print(f"找到位移文件: {os.path.basename(displacement_file)}")
        print(f"找到振幅文件: {os.path.basename(amplitude_file)}")
        print(f"使用位移文件的第{displacement_column_choice + 1}列数据")  # 显示实际使用的列号
        
        # 读取位移数据
        times_disp, displacements, _ = read_wavelet_data(displacement_file, signal_column=displacement_column_choice)
        
        # 读取振幅数据 (469995Hz文件的第三列)
        times_amp, _, amplitudes = read_wavelet_data(amplitude_file)
        
        if len(times_disp) > 0 and len(times_amp) > 0:
            # 检查时间轴是否匹配
            if len(times_disp) == len(times_amp) and np.allclose(times_disp, times_amp, rtol=1e-10):
                print(f"\n时间轴匹配，数据点数: {len(times_disp)}")
                times = times_disp  # 使用统一的时间轴
                
                # 计算动态Us值 (用74999Hz的位移修正za，用469995Hz的振幅计算电压)
                Us_values, z_a_dynamic = calculate_us_dynamic(
                    amplitudes, displacements, k_i, Q_i, epsilon_0, z_a_base, R, U_AC
                )
                
                # 保存电压随时间变化的数据
                voltage_data = np.column_stack((times, Us_values, z_a_dynamic, displacements, amplitudes))
                output_file = f"result_cwtanalyze_dynamic_all/{data_folder}/us_vs_time_dynamic.txt"
                
                header = f"Time(s) Us(V) Z_a_dynamic(m) Displacement_{displacement_frequency}Hz(m) Amplitude_{amplitude_frequency}Hz(m)"
                np.savetxt(output_file, voltage_data, fmt='%.8e', header=header, comments='')
                
                # 检测实际信号周期
                dt = times[1] - times[0]  # 时间步长
                
                print(f"开始周期检测...")
                print(f"时间步长: {dt:.6e} s")
                
                # 使用FFT检测周期
                try:
                    actual_period, actual_freq = detect_signal_period_fft(Us_values, dt)
                    print(f"FFT方法检测: 周期 = {actual_period:.6e} s, 频率 = {actual_freq:.1f} Hz")
                    method_used = 'FFT'
                except Exception as e:
                    print(f"FFT方法出错: {e}")
                    # 如果检测失败，使用默认值
                    actual_freq = amplitude_frequency
                    actual_period = 1.0 / actual_freq
                    method_used = '默认值'
                    print(f"周期检测失败，使用默认频率: {actual_freq} Hz")
                

                
                # 计算每个周期的最大值乘以0.6969
                print(f"\n计算每周期最大值×0.6969...")
                period_samples = int(actual_period / dt)  # 每个周期的采样点数
                print(f"每周期采样点数: {period_samples}")
                
                # 计算完整周期数
                total_samples = len(Us_values)
                complete_periods = total_samples // period_samples
                print(f"完整周期数: {complete_periods}")
                
                # 为每个周期计算max×0.6969
                us_midpoint_values = np.zeros_like(Us_values)
                
                for period_idx in range(complete_periods):
                    start_idx = period_idx * period_samples
                    end_idx = (period_idx + 1) * period_samples
                    period_data = Us_values[start_idx:end_idx]
                    
                    period_max = np.max(period_data)
                    period_scaled = period_max * 0.6969
                    
                    # 将缩放值分配给该周期的所有采样点
                    us_midpoint_values[start_idx:end_idx] = period_scaled
                
                # 处理剩余的不完整周期
                remaining_start = complete_periods * period_samples
                if remaining_start < total_samples:
                    remaining_data = Us_values[remaining_start:]
                    if len(remaining_data) > 0:
                        remaining_max = np.max(remaining_data)
                        remaining_scaled = remaining_max * 0.6969
                        us_midpoint_values[remaining_start:] = remaining_scaled
                        print(f"处理剩余{len(remaining_data)}个采样点")
                
                print(f"缩放后值范围: {np.min(us_midpoint_values):.6e} 到 {np.max(us_midpoint_values):.6e} V")
                

                # 保存中点值结果到新文件
                midpoint_data = np.column_stack((times, us_midpoint_values, z_a_dynamic, displacements, amplitudes))
                midpoint_output_file = f"result_cwtanalyze_dynamic_all/{data_folder}/us_midpoint_vs_time_dynamic.txt"
                
                midpoint_header = f"Time(s) Us_Scaled(V) Z_a_dynamic(m) Displacement_{displacement_frequency}Hz(m) Amplitude_{amplitude_frequency}Hz(m)"
                np.savetxt(midpoint_output_file, midpoint_data, fmt='%.8e', header=midpoint_header, comments='')
                
                print(f"位移数据来源: {displacement_frequency}Hz 文件")
                print(f"振幅数据来源: {amplitude_frequency}Hz 文件")
                print(f"基础z_a: {z_a_base:.2e} m")
                print(f"位移范围: {np.min(displacements):.2e} 到 {np.max(displacements):.2e} m")
                print(f"动态z_a范围: {np.min(z_a_dynamic):.2e} 到 {np.max(z_a_dynamic):.2e} m")
                print(f"振幅范围: {np.min(amplitudes):.2e} 到 {np.max(amplitudes):.2e} m")
                print(f"Us范围: {np.min(Us_values):.2e} 到 {np.max(Us_values):.2e} V")

                print(f"Us 缩放值范围: {np.min(us_midpoint_values):.2e} 到 {np.max(us_midpoint_values):.2e} V")
                print(f"原始电压结果已保存到: {output_file}")

                print(f"缩放值电压结果已保存到: {midpoint_output_file}")
            else:
                print("错误：两个文件的时间轴不匹配，无法合并数据")
                print(f"位移文件数据点数: {len(times_disp)}")
                print(f"振幅文件数据点数: {len(times_amp)}")
        else:
            print("无法读取文件数据，请检查文件格式")
        
        print(f"\n双频率组合计算结果已保存")
        print("\n参数说明:")
        print(f"位移数据来源: {displacement_frequency}Hz文件")
        print(f"振幅数据来源: {amplitude_frequency}Hz文件")
        print(f"k_i = {k_i}")
        print(f"Q_i = {Q_i}")
        print(f"ε₀ = {epsilon_0:.3e}")
        print(f"z_a_base = {z_a_base:.2e} m")
        print(f"R = {R:.2e} m")
        print(f"U_AC = {U_AC} V")
        print("z_a_dynamic = z_a_base + displacement (来自位移数据)")
        print("周期检测: 使用FFT方法检测信号主要频率")
        print("\n文件格式说明:")
        print("原始电压文件 (us_vs_time_dynamic.txt) 包含5列:")
        print("  第1列: 时间(s)")
        print("  第2列: Us电压(V) - 基于469995Hz振幅计算")
        print("  第3列: 动态z_a距离(m) - 基于74999Hz位移修正")
        print("  第4列: 位移数据(m) - 来自74999Hz文件")
        print("  第5列: 振幅数据(m) - 来自469995Hz文件")

        print("\n缩放值电压文件 (us_midpoint_vs_time_dynamic.txt) 包含5列:")
        print("  第1列: 时间(s)")
        print("  第2列: Us_Scaled电压(V) - 每周期max×0.6969")
        print("  第3列: 动态z_a距离(m) - 基于74999Hz位移修正")
        print("  第4列: 位移数据(m) - 来自74999Hz文件")
        print("  第5列: 振幅数据(m) - 来自469995Hz文件") 