% 加载数据
load('extracted_data4.mat');  % 每次都需要改 0
z = extracted_data4;

fs = 1e8;         % 采样频率

% 选择小波变换参数
fmin = 50e3;    % 最小频率
fmax = 6e5;  % 最大频率
df = 10e3;    % 频率间隔
totalscal = (fmax - fmin) / df;  % 总尺度数

f = fmin:df:fmax-df;  % 目标频率

% 小波变换
[coefs, freq] = cwt_cmor(z, 1, 3, f, fs);

% 计算振幅
amplitude = abs(coefs);

% 选择具体频率
targetFreq = 4.8e5;  % 目标频率 (Hz)

% 找到目标频率对应的索引
[~, freqIndex2] = min(abs(freq - targetFreq));  % 计算与目标频率最接近的索引

% 选择时间段（可选）
startTime = 0;  % 起始时间 (秒)
endTime = 0.1;  % 结束时间 (秒)

% 找到时间段对应的索引
N = length(z);  % 信号长度
dt = 1 / fs;  % 采样间隔
time = (0:N-1) * dt;  % 时间向量
startIndex = find(time >= startTime, 1);
endIndex = find(time <= endTime, 1, 'last');

% 提取目标频率的振幅随时间变化
selectedAmplitude2 = abs(coefs(freqIndex2, startIndex:endIndex));  % 提取目标频率的振幅

selectedTime = time(startIndex:endIndex);  % 对应的时间数据

% 绘制目标频率的振幅随时间变化的图
figure;
plot(selectedTime, selectedAmplitude2);
xlabel('时间 (秒)');
ylabel('振幅');
title(['频率 ', num2str(targetFreq), ' Hz 的振幅随时间变化']);

% 提取振幅
y = selectedAmplitude2;  % 将提取到的振幅赋值给 

fs = 1e8;         % 采样频率

% 创建一个时间向量
N = length(z);  % 信号长度
dt = 1 / fs;  % 采样间隔
time = (0:N-1) * dt;  % 时间向量

% 使用希尔伯特变换获取信号的包络线
env = abs(hilbert(z));  % 包络线即振幅

% 绘制振幅随时间变化的图形
figure;
plot(time, env);  % 绘制包络线（振幅）
xlabel('时间 (秒)');
ylabel('振幅');
title('振幅随时间变化')
% 
% % 提取振幅
% % y = selectedAmplitude2;  % 将提取到的振幅赋值给 y
% % 
% % fs = 1e8;  % 采样频率
% % 
% % % 创建一个时间向量
% % N = length(z);  % 信号长度
% % dt = 1 / fs;  % 采样间隔
% % time = (0:N-1) * dt;  % 时间向量
% % 
% % % 确定从0.5毫秒开始的时间段
% % startTime = 0.5e-3;  % 开始时间 0.5毫秒
% % startIndex = find(time >= startTime, 1);  % 找到对应的时间索引
% % 
% % % 截取从 0.5秒后开始的时间和振幅
% % timeSegment = time(startIndex:end);  % 从0.5秒开始的时间段
% % envSegment = env(startIndex:end);    % 对应的包络线（振幅）
% % 
% % % 绘制振幅随时间变化的图形
% % figure;
% % plot(timeSegment, envSegment);  % 绘制包络线（振幅）
% % xlabel('时间 (秒)');
% % ylabel('振幅');
% % title('振幅随时间变化（从0.5毫秒后开始）');
% 
% % 提取振幅
% y = selectedAmplitude2;  % 将提取到的振幅赋值给 y
% 
% fs = 1e8;  % 采样频率
% 
% % 创建时间向量
% N = length(z);  % 信号长度
% dt = 1 / fs;  % 采样间隔
% time = (0:N-1) * dt;  % 时间向量
% 
% % 找出局部最大值（峰）和局部最小值（谷）
% [peaks, peakLocs] = findpeaks(z);  % 找到局部最大值（峰）
% [troughs, troughLocs] = findpeaks(-z);  % 找到局部最小值（谷）
% 
% % 由于找出最小值时取了负值，需要还原
% troughs = -troughs;  
% 
% % 计算振幅：每对最大值和最小值的差值
% amplitude = peaks - troughs;  % 峰-谷振幅
% 
% % 根据最大值和最小值的索引，将振幅与时间对应
% amplitudeTime = time(peakLocs);  % 振幅对应的时间点
% 
% % 绘制振幅随时间变化的图形
% figure;
% plot(amplitudeTime, amplitude);  % 绘制振幅随时间的变化
% xlabel('时间 (秒)');
% ylabel('振幅');
% title('振幅随时间变化（最大值-最小值方法）');


