import numpy as np
import matplotlib.pyplot as plt

# 定义小波函数
def wavelet(av, v, b, a, x):
    return av * np.exp(-((x - b) / a)**2 / 2) * np.cos(v * ((x - b) / a))

# 定义变换函数
def transformation(a, b, av, v, data_array):
    result = 0
    for row in data_array:
        x, multiplier = row
        wavelet_value = wavelet(av, v, b, a, x)
        result += multiplier * wavelet_value
        result = np.log2(abs(result))
    return result

# 定义从文件加载数据的函数
def load_data(file_path):
    data_array = []
    with open(file_path, 'r') as file:
        lines = file.readlines()
        for i, line in enumerate(lines):
            value = float(line.strip())
            data_array.append([(i + 1), value])
    return data_array

# 修改此处的文件路径以指向您的数据文件
file_path = './output1.txt'  # 请确保替换为您的实际文件路径

# 使用文件路径加载数据
data_array = load_data(file_path)

# 设置参数范围
b_values = np.linspace(0, 400000, 10)
a_values = np.linspace(50000, 500000, 10)

A, B = np.meshgrid(a_values, b_values)

# 初始化结果矩阵
Z = np.zeros(A.shape)

# 计算变换结果
for i in range(A.shape[0]):
    for j in range(A.shape[1]):
        Z[i, j] = transformation(A[i, j], B[i, j], av=1, v=5, data_array=data_array)

# 绘制二维热图
plt.figure(figsize=(10, 8))
contour = plt.contourf(A, B, Z, levels=50, cmap='viridis')
plt.colorbar(contour)
plt.xlabel('a Value')
plt.ylabel('b Value')
plt.title('2D Heatmap of Transformation with Color Representing Magnitude')
plt.show()