% 加载数据
load('extracted_data8.mat');  % 每次都需要改 2

% 设置采样频率
Fs = 1e6; % 采样频率 (Hz) % 注意采样率

% 使用 Morlet 小波进行连续小波变换
[cfs, freq] = cwt(extracted_data8, Fs);  % 每次都需要改 1

% 计算振幅
amplitude = abs(cfs);

% 第一个频率范围
minFreq1 = 60000; % 最小频率 (Hz)
maxFreq1 = 75000; % 最大频率 (Hz)

% 第二个频率范围
minFreq2 = 450000; % 最小频率 (Hz)
maxFreq2 = 480000; % 最大频率 (Hz)

% 找到第一个频率范围对应的索引
freqIndex1 = freq >= minFreq1 & freq <= maxFreq1;

% 找到第二个频率范围对应的索引
freqIndex2 = freq >= minFreq2 & freq <= maxFreq2;

% 选择时间段（可选）
startTime = 0; % 起始时间 (秒)
endTime = 0.1; % 结束时间 (秒)

% 找到时间段对应的索引
N = length(extracted_data5);  % 每次都需要改 0 
dt = 1 / Fs; % 采样间隔
time = (0:N-1) * dt; % 时间向量
startIndex = find(time >= startTime, 1);
endIndex = find(time <= endTime, 1, 'last');

% 选择对应的时间和振幅数据
selectedAmplitude1 = amplitude(freqIndex1, startIndex:endIndex); % 第一个频率范围的振幅
selectedAmplitude2 = amplitude(freqIndex2, startIndex:endIndex); % 第二个频率范围的振幅
selectedTime = time(startIndex:endIndex); % 时间数据

% 生成第一张图片：频率范围 65kHz - 80kHz
figure;
plot(selectedTime, selectedAmplitude1);
xlabel('时间 (s)');
ylabel('振幅');
title('第一模态振幅随时间变化');

% 生成第二张图片：频率范围 460kHz - 480kHz
figure;
plot(selectedTime, selectedAmplitude2);
xlabel('时间 (s)');
ylabel('振幅');
title('第二模态振幅随时间变化');


