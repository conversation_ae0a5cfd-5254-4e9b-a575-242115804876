import numpy as np
from scipy.fft import fft, fftfreq
import matplotlib
import pywt
import os
import time  # 添加time模块用于计时
import sys  # 添加sys模块用于重定向输出

# ==================================================
# 参数设置区域 - 可以根据需要进行修改
# ==================================================
# 输入/输出设置
input_filename = 'data20.txt'
base_folder = 'result_cwtnew'

# 小波变换参数
wavelet = 'cmor5.0-1.0'  # 复Morlet小波
edge_exclude_ratio = 0.05  # 排除信号两端各5%的数据

# 采样率
fs = 100e6  # 100MHz采样率

# 电压计算参数
k_i = 7.86  # 悬臂刚度
Q_i = 250.8  # 悬臂品质因子
epsilon_0 = 8.854e-12  # 真空介电常数
z_a = 120e-9  # 探针抬升高度
R = 20e-9  # 探针针尖半径
U_AC = 5  # 交流激励电压的幅值

# 数据处理参数
points_per_group = 2000  # 每组点数，用于平滑处理

# 窗函数选择和配置
window_functions = {
    'hanning': np.hanning,
    'hamming': np.hamming, 
    'blackman': np.blackman
}
selected_window = 'hamming'  # 可选：'hanning', 'hamming', 'blackman'

# 局部FFT窗口长度
window_half_width_extended = 0.1e-3  # 使用±0.1毫秒的窗口（原来是0.5毫秒）

# 局部频率分析方法选择
# 'fft': 传统FFT
# 'stft': 短时傅里叶变换
frequency_analysis_method = 'fft'  # 改回使用传统FFT方法
stft_window_length = 2048  # STFT窗口长度，2的幂次方效率更高
stft_overlap = 0.75  # STFT窗口重叠率 (0-1之间)

# 配置matplotlib支持中文显示
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'SimSun', 'FangSong']
matplotlib.rcParams['axes.unicode_minus'] = False

# 添加电压计算函数
def calculate_us(A, k_i, Q_i, epsilon_0, z_a, R, U_AC):
    """
    根据给定的振幅A计算Us（只考虑共振项）
    
    参数:
    A: 振幅
    k_i: k_i参数
    Q_i: Q_i参数
    epsilon_0: ε₀ 真空介电常数
    z_a: z_a参数
    R: R参数
    U_AC: U_AC参数
    
    返回:
    Us: 计算得到的Us值，可以是单个值或数组
    """
    denominator_term = (z_a/R)**2 + z_a/R
    C = k_i / (2 * np.pi * epsilon_0 * Q_i)
    Us = C * A * denominator_term / U_AC
    return Us

def smooth_by_average(times, values, points_per_group=10):
    """
    对数据每N个点取平均值进行平滑处理
    
    参数:
    times: 时间数组
    values: 数值数组
    points_per_group: 每组的点数（默认10个点）
    
    返回:
    smoothed_times: 平滑后的时间数组
    smoothed_values: 平滑后的数值数组
    """
    n = len(values)
    n_groups = n // points_per_group
    remainder = n % points_per_group
    
    print(f"数据平滑处理:")
    print(f"  原始数据点数: {n}")
    print(f"  每组点数: {points_per_group}")
    print(f"  完整组数: {n_groups}")
    print(f"  剩余点数: {remainder}")
    
    smoothed_times = []
    smoothed_values = []
    
    # 处理完整的组
    for i in range(n_groups):
        start_idx = i * points_per_group
        end_idx = (i + 1) * points_per_group
        
        # 取每组的时间中点和数值平均值
        group_time_avg = np.mean(times[start_idx:end_idx])
        group_value_avg = np.mean(values[start_idx:end_idx])
        
        smoothed_times.append(group_time_avg)
        smoothed_values.append(group_value_avg)
    
    # 处理剩余的点
    if remainder > 0:
        start_idx = n_groups * points_per_group
        group_time_avg = np.mean(times[start_idx:])
        group_value_avg = np.mean(values[start_idx:])
        
        smoothed_times.append(group_time_avg)
        smoothed_values.append(group_value_avg)
        print(f"  处理剩余{remainder}个点作为最后一组")
    
    smoothed_times = np.array(smoothed_times)
    smoothed_values = np.array(smoothed_values)
    
    print(f"  平滑后数据点数: {len(smoothed_values)}")
    print(f"  数据压缩比: {len(smoothed_values)/n:.3f}")
    
    return smoothed_times, smoothed_values

def stft_analysis(t, x, window_center_time, target_freq, fs, window_function='hamming', 
                 nperseg=2048, noverlap=None, search_width=2000):
    """
    使用短时傅里叶变换(STFT)在指定时间点附近分析信号
    
    参数:
    t: 时间数组
    x: 信号数组
    window_center_time: 窗口中心时间
    target_freq: 目标频率
    fs: 采样频率
    window_function: 窗函数类型
    nperseg: 每段长度
    noverlap: 重叠点数
    search_width: 频率搜索范围
    
    返回:
    peak_freq: 峰值频率
    peak_mag: 峰值幅值
    time_idx: 最接近中心时间的STFT时间索引
    """
    from scipy.signal import stft
    
    if noverlap is None:
        noverlap = int(nperseg * stft_overlap)
    
    # 执行STFT
    f, t_stft, Zxx = stft(x, fs=fs, window=window_function, nperseg=nperseg, 
                          noverlap=noverlap, return_onesided=True)
    
    # 找到离窗口中心时间最近的STFT时间索引
    time_idx = np.argmin(np.abs(t_stft - window_center_time))
    
    # 提取该时间点的频谱
    magnitude = np.abs(Zxx[:, time_idx])
    
    # 在目标频率附近搜索最大幅值
    freq_mask = (f >= target_freq - search_width) & (f <= target_freq + search_width)
    if np.any(freq_mask):
        search_freqs = f[freq_mask]
        search_mags = magnitude[freq_mask]
        
        peak_idx = np.argmax(search_mags)
        peak_freq = search_freqs[peak_idx]
        peak_mag = search_mags[peak_idx]
        
        # 频率分辨率
        freq_resolution = fs / nperseg
        
        return peak_freq, peak_mag, time_idx, freq_resolution
    else:
        return None, None, time_idx, fs / nperseg

# 创建结果文件夹和输出日志文件
data_name = os.path.splitext(input_filename)[0]  # 从'data02.txt'提取'data02'
result_folder = os.path.join(base_folder, data_name)
os.makedirs(result_folder, exist_ok=True)
log_file_path = os.path.join(result_folder, "processing_log.txt")

# 重定向输出到文件
original_stdout = sys.stdout
log_file = open(log_file_path, 'w')
sys.stdout = log_file

# 开始记录总运行时间
total_start_time = time.time()
# 初始化纯计算时间计数器
pure_compute_time = 0

# 读取数据文件
data = np.loadtxt(input_filename)
t = data[:, 0]  # 第一列为时间
x = data[:, 1]  # 第二列为信号

# 添加去均值处理
x = x - np.mean(x)

# 打印原始信号的统计信息
print("\n=== 原始信号统计 ===")
print(f"数据点数: {len(t)}, 时间步长: {t[1] - t[0]:.6e}s")
print(f"数据最大值: {np.max(x):.6e}, 最小值: {np.min(x):.6e}")

# =========================================
# 对全部数据进行FFT分析，获取主要频率
# =========================================
print("\n=== 全部数据FFT分析 ===")

# 记录FFT计算开始时间
fft_compute_start = time.time()

# 对全部数据计算FFT
X = fft(x)
freqs = fftfreq(len(x), 1 / fs)
magnitude = np.abs(X) / len(x)

# 只保留正频率部分
positive_freq_mask = freqs > 0
pos_freqs = freqs[positive_freq_mask]
pos_magnitude = magnitude[positive_freq_mask]

# 使用单边谱处理
if len(pos_magnitude) > 1:
    pos_magnitude[1:-1] = pos_magnitude[1:-1] * 2

# 记录FFT计算结束时间
fft_compute_end = time.time()
fft_compute_time = fft_compute_end - fft_compute_start
pure_compute_time += fft_compute_time

# 不保存全部数据的FFT分析结果到文件（保留在内存中用于后续分析）

# 直接找到第一个（最大）峰值
first_peak_idx = np.argmax(pos_magnitude)
first_freq = pos_freqs[first_peak_idx]
first_mag = pos_magnitude[first_peak_idx]

# 计算预期的第二个频率（约为第一个频率的6.27倍）
expected_second_freq = first_freq * 6.27
search_width = 2000  # 搜索范围：目标频率±1000Hz

# 在预期频率附近搜索第二个峰值
freq_mask = (pos_freqs >= expected_second_freq - search_width) & (
            pos_freqs <= expected_second_freq + search_width)
search_freqs = pos_freqs[freq_mask]
search_mags = pos_magnitude[freq_mask]

# 检查是否找到第二个峰值
if len(search_mags) > 0:
    # 在搜索范围内找到最大幅值
    second_peak_idx = np.argmax(search_mags)
    second_freq = search_freqs[second_peak_idx]
    second_mag = search_mags[second_peak_idx]

    # 将结果存储在main_freqs和main_mags中
    main_freqs = np.array([first_freq, second_freq])
    main_mags = np.array([first_mag, second_mag])

    print("检测到的主要频率:")
    print(f"  {main_freqs[0]:8.6f}Hz  {main_mags[0]:8.6e}")
    print(f"  {main_freqs[1]:8.6f}Hz  {main_mags[1]:8.6e}  (比值: {main_freqs[1] / main_freqs[0]:.6f})")
else:
    print(f"警告：未找到第二个峰值，只分析第一个频率")
    main_freqs = np.array([first_freq])
    main_mags = np.array([first_mag])

# 保存主要频率信息
with open(os.path.join(result_folder, 'frequency_info.txt'), 'w', encoding='utf-8') as f:
    f.write("检测到的主要频率(基于全部数据):\n")
    f.write("频率(Hz)    幅值    频率比\n")
    f.write("-" * 40 + "\n")
    for i, freq in enumerate(main_freqs):
        if i == 0:
            f.write(f"{freq:8.2f}  {main_mags[i]:8.4e}  (基准频率)\n")
        else:
            f.write(f"{freq:8.2f}  {main_mags[i]:8.4e}  {freq / main_freqs[0]:8.3f}\n")

# =========================================
# 使用小波变换分析所有检测到的频率成分
# =========================================
print(f"\n=== 小波变换分析 ===")

# 记录小波变换开始时间
pywt_start_time = time.time()

# 使用复Morlet小波
central_freq = pywt.central_frequency(wavelet)

# 为每个主要频率计算对应的尺度
scales = []
for freq in main_freqs:
    scale = (central_freq * fs) / freq
    scales.append(scale)

# 对每个主要频率进行小波变换
wavelet_results = {}
actual_freqs = []  # 存储实际计算的频率
max_amplitude_time_windows = {}  # 存储每个频率最大幅值时间窗口

for i, (freq, scale) in enumerate(zip(main_freqs, scales)):
    # 对完整信号进行小波变换
    wavelet_compute_start = time.time()
    coeffs, frequencies = pywt.cwt(x, [scale], wavelet, sampling_period=1 / fs, method='fft')
    wavelet_compute_end = time.time()
    wavelet_compute_time = wavelet_compute_end - wavelet_compute_start
    pure_compute_time += wavelet_compute_time

    # 获取返回的实际频率值
    actual_freq = frequencies[0]  # 小波变换返回的精确频率值
    actual_freqs.append(actual_freq)
    
    # 计算小波变换幅值
    amplitude = np.abs(coeffs[0])
    
    # 计算中间区域的范围（排除边缘数据）
    data_len = len(amplitude)
    edge_size = int(data_len * edge_exclude_ratio)
    center_start = edge_size
    center_end = data_len - edge_size
    
    # 只使用中间部分数据找最大幅值点
    center_data = amplitude[center_start:center_end]
    max_amp_idx_center = np.argmax(center_data) + center_start  # 调整回全局索引
    max_amp_time = t[max_amp_idx_center]
    max_amp_value = amplitude[max_amp_idx_center]
    
    print(f"\n频率分量 {i + 1}:")
    print(f"  最大幅值时间点: {max_amp_time*1000:.6f}ms")
    print(f"  最大幅值: {max_amp_value:.6e}")
    
    # 在最大幅值点附近定义时间窗口 (±0.5ms)
    window_half_width = 0.5e-3  # 0.5毫秒
    window_start_time = max_amp_time - window_half_width
    window_end_time = max_amp_time + window_half_width
    
    # 确保窗口在有效数据范围内
    window_start_time = max(t[0], window_start_time)
    window_end_time = min(t[-1], window_end_time)
    
    # 找到对应的索引
    window_mask = (t >= window_start_time) & (t <= window_end_time)
    window_t = t[window_mask]
    window_x = x[window_mask]
    
    print(f"  时间窗口: {window_start_time*1000:.6f}-{window_end_time*1000:.6f}ms, 数据点: {len(window_x)}")
    
    # 保存最大幅值窗口信息，用于后续处理
    max_amplitude_time_windows[actual_freq] = {
        'window_start_time': window_start_time,
        'window_end_time': window_end_time,
        'window_mask': window_mask,
        'window_t': window_t,
        'window_x': window_x,
        'max_amp_time': max_amp_time,
        'max_amp_value': max_amp_value,
        'max_amp_idx': max_amp_idx_center
    }
    
    # 存储小波变换结果
    wavelet_results[actual_freq] = {
        'signal': np.real(coeffs[0]),
        'amplitude': amplitude,
        'target_freq': freq,  # 保存目标频率
        'actual_freq': actual_freq,
        'input_scale': scale,
        'frequencies': frequencies  # 保存完整的frequencies数组
    }

# =========================================
# 在最大幅值时间窗口进行局部频率分析
# =========================================
print(f"\n=== 局部频率分析 (方法: {frequency_analysis_method}) ===")

local_fft_results = {}
for i, actual_freq in enumerate(actual_freqs):
    window_info = max_amplitude_time_windows[actual_freq]
    original_window_t = window_info['window_t']
    original_window_x = window_info['window_x']
    max_amp_time = window_info['max_amp_time']
    
    # 重新定义扩展的时间窗口
    extended_window_start_time = max_amp_time - window_half_width_extended
    extended_window_end_time = max_amp_time + window_half_width_extended
    
    # 确保窗口在有效数据范围内
    extended_window_start_time = max(t[0], extended_window_start_time)
    extended_window_end_time = min(t[-1], extended_window_end_time)
    
    # 找到对应的索引
    extended_window_mask = (t >= extended_window_start_time) & (t <= extended_window_end_time)
    extended_window_t = t[extended_window_mask]
    extended_window_x = x[extended_window_mask]
    
    print(f"\n频率 {actual_freq:.6f}Hz 的窗口:")
    print(f"  原始窗口: {window_info['window_start_time']*1000:.6f}-{window_info['window_end_time']*1000:.6f}ms, 数据点: {len(original_window_x)}")
    print(f"  扩展窗口: {extended_window_start_time*1000:.6f}-{extended_window_end_time*1000:.6f}ms, 数据点: {len(extended_window_x)}")
    
    # 记录计算开始时间
    local_analysis_start = time.time()
    
    # 使用不同的局部频率分析方法
    target_freq = wavelet_results[actual_freq]['target_freq']
    
    if len(extended_window_x) > 0:
        if frequency_analysis_method == 'fft':
            # 使用传统FFT方法
            # 应用窗函数
            window_func = window_functions[selected_window](len(extended_window_x))
            windowed_signal = extended_window_x * window_func
            
            # 计算窗函数的功率补偿因子
            window_power_correction = len(window_func) / np.sum(window_func)
            
            # 计算局部FFT
            window_X = fft(windowed_signal)
            window_freqs = fftfreq(len(windowed_signal), 1 / fs)
            window_magnitude = np.abs(window_X) / len(windowed_signal)
            
            # 应用窗函数功率补偿
            window_magnitude = window_magnitude * window_power_correction
            
            # 只保留正频率部分
            window_pos_mask = window_freqs > 0
            window_pos_freqs = window_freqs[window_pos_mask]
            window_pos_magnitude = window_magnitude[window_pos_mask]
            
            # 使用单边谱处理
            if len(window_pos_magnitude) > 1:
                window_pos_magnitude[1:-1] = window_pos_magnitude[1:-1] * 2
            
            # 在目标频率附近搜索最大幅值
            search_width = 2000  # 搜索范围：目标频率±1000Hz
            
            freq_mask = (window_pos_freqs >= target_freq - search_width) & (window_pos_freqs <= target_freq + search_width)
            if np.any(freq_mask):  # 确保有数据在搜索范围内
                search_freqs = window_pos_freqs[freq_mask]
                search_mags = window_pos_magnitude[freq_mask]
                
                # 在搜索范围内找到最大幅值
                peak_idx = np.argmax(search_mags)
                local_peak_freq = search_freqs[peak_idx]
                local_peak_mag = search_mags[peak_idx]
                
                # 计算频率分辨率
                freq_resolution = fs / len(windowed_signal)
                
                final_peak_freq = local_peak_freq
                final_peak_mag = local_peak_mag
                
                print(f"  局部FFT峰值: {local_peak_mag:.6e} ({local_peak_freq:.6f}Hz)")
                print(f"  频率分辨率: {freq_resolution:.6f}Hz")
                
                # 保存局部FFT结果
                local_fft_results[actual_freq] = {
                    'peak_freq': final_peak_freq,
                    'peak_mag': final_peak_mag,
                    'freqs': window_pos_freqs,
                    'magnitude': window_pos_magnitude,
                    'window_function': selected_window,
                    'window_length': len(extended_window_x),
                    'freq_resolution': freq_resolution,
                    'power_correction': window_power_correction,
                    'method': 'fft'
                }
            
            else:
                print(f"  警告：在目标频率附近未找到峰值")
                local_fft_results[actual_freq] = None
                
        elif frequency_analysis_method == 'stft':
            # 使用短时傅里叶变换(STFT)方法
            print(f"  使用STFT分析, 窗口长度: {stft_window_length}, 重叠率: {stft_overlap}")
            
            # 执行STFT分析
            peak_freq, peak_mag, time_idx, freq_resolution = stft_analysis(
                t, x, max_amp_time, target_freq, fs, 
                window_function=selected_window, 
                nperseg=stft_window_length, 
                search_width=2000
            )
            
            if peak_freq is not None:
                print(f"  STFT峰值: {peak_mag:.6e} ({peak_freq:.6f}Hz)")
                print(f"  频率分辨率: {freq_resolution:.6f}Hz")
                
                # 保存STFT结果
                local_fft_results[actual_freq] = {
                    'peak_freq': peak_freq,
                    'peak_mag': peak_mag,
                    'window_function': selected_window,
                    'window_length': stft_window_length,
                    'freq_resolution': freq_resolution,
                    'method': 'stft'
                }
            else:
                print(f"  警告：在目标频率附近未找到峰值")
                local_fft_results[actual_freq] = None
        
        # 记录计算结束时间
        local_analysis_end = time.time()
        local_analysis_time = local_analysis_end - local_analysis_start
        pure_compute_time += local_analysis_time
            
    else:
        print(f"  警告：扩展窗口内没有有效数据点")
        local_fft_results[actual_freq] = None

# =========================================
# 使用局部频率分析的峰值幅值对小波变换结果进行归一化
# =========================================
print("\n=== 校准和归一化 ===")

normalized_wavelet_results = {}
for i, actual_freq in enumerate(actual_freqs):
    local_result = local_fft_results[actual_freq]
    if local_result is None:
        print(f"跳过频率 {actual_freq:.6f}Hz 的归一化，因为局部频率分析中未找到有效峰值")
        continue
    
    wavelet_result = wavelet_results[actual_freq]
    window_info = max_amplitude_time_windows[actual_freq]
    
    # 获取小波变换在最大幅值点的幅度
    wavelet_amp_at_max = window_info['max_amp_value']
    
    # 使用局部频率分析的峰值幅值计算归一化因子
    norm_factor = local_result['peak_mag'] / wavelet_amp_at_max
    
    print(f"\n频率 {actual_freq:.6f}Hz 的归一化:")
    print(f"  小波变换峰值: {wavelet_amp_at_max:.6e}")
    print(f"  局部频率分析峰值: {local_result['peak_mag']:.6e} (方法: {local_result['method']})")
    print(f"  归一化因子: {norm_factor:.6f}")
    
    # 归一化小波变换结果
    normalized_signal = wavelet_result['signal'] * norm_factor
    normalized_amplitude = wavelet_result['amplitude'] * norm_factor
    
    # 保存归一化结果
    normalized_wavelet_results[actual_freq] = {
        'signal': normalized_signal,
        'amplitude': normalized_amplitude,
        'norm_factor': norm_factor,
        'target_freq': wavelet_result['target_freq'],
        'actual_freq': actual_freq,
        'local_freq_peak': local_result['peak_mag'],
        'freq_analysis_method': local_result['method']
    }
    
    # 保存归一化后的小波变换结果
    filename = f'wavelet_{actual_freq:.0f}Hz.txt'
    wavelet_data = np.column_stack((
        t,
        normalized_signal,
        normalized_amplitude
    ))
    target_freq = wavelet_result['target_freq']
    np.savetxt(os.path.join(result_folder, filename), wavelet_data,
               header=f'Time(s) Signal(target={target_freq:.2f}Hz,actual={actual_freq:.10f}Hz) Amplitude',
               fmt='%.8f %.8e %.8e',
               delimiter=' ',
               comments='')

# =========================================
# 计算电压值
# =========================================
print("\n=== 电压值计算 ===")

# 查找470285Hz附近的频率文件
target_freq_for_voltage = 470285  # 目标频率
voltage_calculation_freq = None
voltage_file_path = None

# 在归一化结果中查找最接近470285Hz的频率
min_freq_diff = float('inf')
for actual_freq in normalized_wavelet_results.keys():
    freq_diff = abs(actual_freq - target_freq_for_voltage)
    if freq_diff < min_freq_diff:
        min_freq_diff = freq_diff
        voltage_calculation_freq = actual_freq

if voltage_calculation_freq is not None:
    voltage_file_path = os.path.join(result_folder, f'wavelet_{voltage_calculation_freq:.0f}Hz.txt')
    print(f"\n选择用于电压计算数据文件: ")
    print(f"{voltage_file_path}")
    print()

    try:
        # 直接读取已保存的数据
        wavelet_data = np.loadtxt(voltage_file_path, skiprows=1)  # 跳过标题行
        
        if wavelet_data.shape[1] >= 3:  # 确保有足够的列
            times = wavelet_data[:, 0]  # 第一列是时间
            amplitudes = wavelet_data[:, 2]  # 第三列是归一化幅值
            
            # 直接计算Us值
            print("\n步骤2: 使用公式计算Us值...")
            Us_values_raw = calculate_us(amplitudes, k_i, Q_i, epsilon_0, z_a, R, U_AC)
            
            # 保存原始电压数据
            voltage_data_raw = np.column_stack((times, Us_values_raw))
            output_file_raw = os.path.join(result_folder, "us_vs_time.txt")
            np.savetxt(output_file_raw, voltage_data_raw, fmt='%.8e', 
                      header='Time(s) Us_Original(V)', comments='')
            print(f"原始电压数据已保存到: {output_file_raw}")
            
            # 步骤3: 每组取平均值平滑处理
            print(f"\n步骤3: 每{points_per_group}个点取平均值平滑处理...")
            times_smoothed, Us_values_smoothed = smooth_by_average(
                times, Us_values_raw, points_per_group
            )
            
            # 保存平滑后的电压数据
            voltage_data_smoothed = np.column_stack((times_smoothed, Us_values_smoothed))
            output_file_smoothed = os.path.join(result_folder, "us_vs_time_smoothed.txt")
            np.savetxt(output_file_smoothed, voltage_data_smoothed, fmt='%.8e', 
                      header='Time(s) Us_Smoothed(V)', comments='')
            print(f"平滑后电压数据已保存到: {output_file_smoothed}")
            
        else:
            print(f"错误：数据文件格式不正确，列数不足")
            
    except Exception as e:
        print(f"电压计算失败: {str(e)}")
        
else:
    print(f"警告：未找到适合进行电压计算的频率文件")

# 计算总运行时间
total_end_time = time.time()
total_elapsed_time = total_end_time - total_start_time
io_time = total_elapsed_time - pure_compute_time

print(f"\n=== 完成 ===")
print(f"数据已保存到文件夹: {result_folder}")
print(f"总耗时: {total_elapsed_time:.6f}s (计算: {pure_compute_time:.6f}s, IO: {io_time:.6f}s)")

# 列出所有输出文件
print(f"\n输出文件列表:")
print(f"1. frequency_info.txt - 主要频率信息")
file_counter = 2
for i, actual_freq in enumerate(actual_freqs):
    print(f"{file_counter}. wavelet_{actual_freq:.0f}Hz.txt - {actual_freq:.0f}Hz分量的小波分析结果")
    file_counter += 1

# 列出电压结果文件（如果成功计算）
if voltage_calculation_freq is not None:
    print(f"{file_counter}. us_vs_time.txt - 原始电压数据")
    file_counter += 1
    print(f"{file_counter}. us_vs_time_smoothed.txt - 平滑后电压数据")
    file_counter += 1
    print(f"{file_counter}. processing_log.txt - 处理日志信息")

# 恢复标准输出
sys.stdout = original_stdout
log_file.close()

# 读取日志文件并打印内容
with open(log_file_path, 'r') as f:
    log_content = f.read()
    print(log_content)

print(f"处理完成！结果保存在：{result_folder}")
print(f"处理日志保存在：{log_file_path}")

