% 直接展示yout振动信号随时间变化
% 使用方法：先运行SimRun_p2，然后运行此脚本

% 检查yout变量
if ~exist('yout', 'var')
    error('工作区中未找到yout变量！请先运行 SimRun_p2 生成仿真数据。');
end

% 设置采样率
Fs = 1e8;  % 100MHz

% 处理数据
if isstruct(yout)
    if isfield(yout, 'Time') && isfield(yout, 'Data')
        t = yout.Time;
        y = yout.Data;
    else
        y = yout;
        t = (0:length(y)-1) / Fs;
    end
else
    y = yout;
    t = (0:length(y)-1) / Fs;
end

% 绘制信号
figure;
plot(t * 1000, y * 1e9, 'b-', 'LineWidth', 1);
xlabel('时间/ms');
ylabel('振动幅度/nm');
title('探针振动信号');
grid on; 