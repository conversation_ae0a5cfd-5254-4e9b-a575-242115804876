import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from multiprocessing import Pool

def wavelet(av, v, b, a, x):
    """
    计算给定参数的波形值。
    """
    return av * np.exp(-((x - b) / a)**2 / 2) * np.cos(v * ((x - b) / a))

def transformation(args):
    """
    根据给定参数和数据数组，计算特定的函数值。
    使用 *args 来允许 map 传递多个参数。
    """
    a, b, av, v, data_array = args
    result = 0
    for row in data_array:
        x, multiplier = row
        wavelet_value = wavelet(av, v, b, a, x)
        result += multiplier * wavelet_value
       # print(f"Processing a={a}, b={b}\n")
    return result

def load_data(file_path):
    """
    从文件加载数据到二维数组。
    """
    data_array = []
    with open(file_path, 'r') as file:
        lines = file.readlines()
        for i, line in enumerate(lines):
            value = float(line.strip())
            data_array.append([(i + 1), value])
    return data_array

file_path = './output1.txt'
data_array = load_data(file_path)

b_values = np.linspace(0, 400000, 10)
a_values = np.linspace(50000, 500000, 10)

A, B = np.meshgrid(a_values, b_values)
Z = np.zeros(A.shape)

# 准备 (a, b) 参数对和其他固定参数
params = [(A[i, j], B[i, j], 1, 5, data_array) for i in range(A.shape[0]) for j in range(A.shape[1])]

# 使用 Pool 对象来并行处理
if __name__ == "__main__":
    with Pool(processes=12) as pool:  # 可以调整进程数以适应您的机器
        results = pool.map(transformation, params)
    
    # 将扁平结果数组转换回 Z 的形状
    Z = np.array(results).reshape(A.shape)
    
    # 绘制三维图
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')
    ax.plot_surface(A, B, Z, cmap='viridis')
    
    ax.set_xlabel('a Value')
    ax.set_ylabel('b Value')
    ax.set_zlabel('f(a, b)')
    plt.title('3D plot of f(a, b)')
    plt.show()