import numpy as np
from scipy.fft import fft, fftfreq
import matplotlib
import pywt
import os
import time  # 添加time模块用于计时

# ==================================================
# 参数设置区域 - 可以根据需要进行修改
# ==================================================
# 输入/输出设置
input_filename = 'data01.txt'
base_folder = 'result_cwtmulti'

# 小波变换参数
wavelet = 'cmor2.5-1.0'  # 复Morlet小波
edge_exclude_ratio = 0.01  # 排除信号两端各2%的数据

# 采样率
fs = 100e6  # 100MHz采样率

# 电压计算参数
k_i = 7.86  # 悬臂刚度
Q_i = 250.8  # 悬臂品质因子
epsilon_0 = 8.854e-12  # 真空介电常数
z_a = 120e-9  # 探针抬升高度
R = 20e-9  # 探针针尖半径
U_AC = 3  # 交流激励电压的幅值
correction_factor = 1.2237  # 电压计算校正因子

# 数据处理参数
trim_percent = 0  # 去掉两端的百分比
points_per_group = 2000  # 每组点数，用于平滑处理

# 窗函数选择和配置
window_functions = {
    'hanning': np.hanning,
    'hamming': np.hamming, 
    'blackman': np.blackman
}
selected_window = 'hamming'  # 可选：'hanning', 'hamming', 'blackman'

# 窗口宽度设置
window_half_width_extended = 1.0e-3  # 1.0毫秒半宽

# ==================================================
# 配置matplotlib支持中文显示
# ==================================================
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'SimSun', 'FangSong']
matplotlib.rcParams['axes.unicode_minus'] = False

# 添加从amplitude_to_us.py复制的电压计算函数
def calculate_us(A, k_i, Q_i, epsilon_0, z_a, R, U_AC, correction_factor=1.0):
    """
    根据给定的振幅A计算Us（只考虑共振项）
    
    参数:
    A: 振幅
    k_i: k_i参数
    Q_i: Q_i参数
    epsilon_0: ε₀ 真空介电常数
    z_a: z_a参数
    R: R参数
    U_AC: U_AC参数
    correction_factor: 校正因子
    
    返回:
    Us: 计算得到的Us值，可以是单个值或数组
    """
    denominator_term = (z_a/R)**2 + z_a/R
    C = k_i / (2 * np.pi * epsilon_0 * Q_i)
    Us = C * A * denominator_term / U_AC * correction_factor
    return Us

def trim_data_edges(times, values, trim_percent=5):
    """
    去掉数据两端的百分比
    
    参数:
    times: 时间数组
    values: 数值数组  
    trim_percent: 要去掉的百分比（默认5%）
    
    返回:
    trimmed_times, trimmed_values: 去掉两端后的数据
    """
    n = len(times)
    trim_count = int(n * trim_percent / 100)
    
    if trim_count * 2 >= n:
        print(f"警告: 去掉两端{trim_percent}%的数据会移除所有数据点，保持原数据")
        return times, values
    
    start_idx = trim_count
    end_idx = n - trim_count
    
    print(f"去掉数据两端{trim_percent}%: 移除前{trim_count}个点和后{trim_count}个点")
    print(f"原始数据点数: {n}, 处理后数据点数: {end_idx - start_idx}")
    
    return times[start_idx:end_idx], values[start_idx:end_idx]

def smooth_by_average(times, values, points_per_group=10):
    """
    对数据每N个点取平均值进行平滑处理
    
    参数:
    times: 时间数组
    values: 数值数组
    points_per_group: 每组的点数（默认10个点）
    
    返回:
    smoothed_times: 平滑后的时间数组
    smoothed_values: 平滑后的数值数组
    """
    n = len(values)
    n_groups = n // points_per_group
    remainder = n % points_per_group
    
    print(f"数据平滑处理:")
    print(f"  原始数据点数: {n}")
    print(f"  每组点数: {points_per_group}")
    print(f"  完整组数: {n_groups}")
    print(f"  剩余点数: {remainder}")
    
    smoothed_times = []
    smoothed_values = []
    
    # 处理完整的组
    for i in range(n_groups):
        start_idx = i * points_per_group
        end_idx = (i + 1) * points_per_group
        
        # 取每组的时间中点和数值平均值
        group_time_avg = np.mean(times[start_idx:end_idx])
        group_value_avg = np.mean(values[start_idx:end_idx])
        
        smoothed_times.append(group_time_avg)
        smoothed_values.append(group_value_avg)
    
    # 处理剩余的点
    if remainder > 0:
        start_idx = n_groups * points_per_group
        group_time_avg = np.mean(times[start_idx:])
        group_value_avg = np.mean(values[start_idx:])
        
        smoothed_times.append(group_time_avg)
        smoothed_values.append(group_value_avg)
        print(f"  处理剩余{remainder}个点作为最后一组")
    
    smoothed_times = np.array(smoothed_times)
    smoothed_values = np.array(smoothed_values)
    
    print(f"  平滑后数据点数: {len(smoothed_values)}")
    print(f"  数据压缩比: {len(smoothed_values)/n:.3f}")
    
    return smoothed_times, smoothed_values

# 开始记录总运行时间
total_start_time = time.time()
# 初始化纯计算时间计数器
pure_compute_time = 0

# 创建结果文件夹
data_name = os.path.splitext(input_filename)[0]  # 从'data10.txt'提取'data10'
result_folder = os.path.join(base_folder, data_name)
os.makedirs(result_folder, exist_ok=True)

# 读取数据文件
data = np.loadtxt(input_filename)
t = data[:, 0]  # 第一列为时间
x = data[:, 1]  # 第二列为信号

# 添加去均值处理
x = x - np.mean(x)

# 打印原始信号的统计信息
print("\n=== 原始信号统计 ===")
print(f"数据点数: {len(t)}, 时间步长: {t[1] - t[0]:.6e}s")
print(f"数据最大值: {np.max(x):.6e}")

# 初始化变量
fft_peaks_8_5_9_5 = {}
wavelet_peaks_8_5_9_5 = {}

# =========================================
# 对全部数据进行FFT分析，获取主要频率
# =========================================
print("\n=== 全部数据FFT分析 ===")

# 记录FFT计算开始时间
fft_compute_start = time.time()

# 对全部数据计算FFT
X = fft(x)
freqs = fftfreq(len(x), 1 / fs)
amplitude = np.abs(X) / len(x)  # 这是原始幅值

# 只保留正频率部分
positive_freq_mask = freqs > 0
pos_freqs = freqs[positive_freq_mask]
pos_amplitude = amplitude[positive_freq_mask]

# 使用单边谱处理 - 乘以2是为了保持能量，所有正频率分量都乘以2（除了0频率）
if len(pos_amplitude) > 1:
    pos_amplitude = pos_amplitude * 2  # 修正：所有正频率都乘以2

# 记录FFT计算结束时间
fft_compute_end = time.time()
fft_compute_time = fft_compute_end - fft_compute_start
pure_compute_time += fft_compute_time

# 直接找到第一个（最大）峰值
first_peak_idx = np.argmax(pos_amplitude)
first_freq = pos_freqs[first_peak_idx]
first_mag = pos_amplitude[first_peak_idx]

# 计算预期的第二个频率（约为第一个频率的6.27倍）
expected_second_freq = first_freq * 6.27
search_width = 2000  # 搜索范围：目标频率±1000Hz

# 在预期频率附近搜索第二个峰值
freq_mask = (pos_freqs >= expected_second_freq - search_width) & (
        pos_freqs <= expected_second_freq + search_width)
search_freqs = pos_freqs[freq_mask]
search_mags = pos_amplitude[freq_mask]

# 检查是否找到第二个峰值
if len(search_mags) > 0:
    # 在搜索范围内找到最大幅值
    second_peak_idx = np.argmax(search_mags)
    second_freq = search_freqs[second_peak_idx]
    second_mag = search_mags[second_peak_idx]

    # 将结果存储在main_freqs和main_mags中
    main_freqs = np.array([first_freq, second_freq])
    main_mags = np.array([first_mag, second_mag])

    print("检测到的主要频率:")
    print(f"  {main_freqs[0]:8.6f}Hz  {main_mags[0]:8.6e}")
    print(f"  {main_freqs[1]:8.6f}Hz  {main_mags[1]:8.6e}  (比值: {main_freqs[1] / main_freqs[0]:.6f})")
else:
    print(f"警告：未找到第二个峰值，只分析第一个频率")
    main_freqs = np.array([first_freq])
    main_mags = np.array([first_mag])

# 保存主要频率信息
with open(os.path.join(result_folder, 'frequency_info.txt'), 'w', encoding='utf-8') as f:
    f.write("检测到的主要频率(基于全部数据):\n")
    f.write("频率(Hz)    幅值    频率比\n")
    f.write("-" * 40 + "\n")
    for i, freq in enumerate(main_freqs):
        if i == 0:
            f.write(f"{freq:8.2f}  {main_mags[i]:8.4e}  (基准频率)\n")
        else:
            f.write(f"{freq:8.2f}  {main_mags[i]:8.4e}  {freq / main_freqs[0]:8.3f}\n")

# =========================================
# 步骤1: 在8.5ms到9.5ms时间段进行FFT分析
# =========================================
print("\n=== 扩展窗口FFT分析 ===")

# 扩展时间窗口以提高频率分辨率
window_center_time = 9.0e-3  # 以9ms为中心
window_half_width = 1.0e-3   # ±1ms，总共2ms窗口
window_start_time = window_center_time - window_half_width  # 8.0ms
window_end_time = window_center_time + window_half_width    # 10.0ms

# 确保窗口在数据范围内
window_start_time = max(t[0], window_start_time)
window_end_time = min(t[-1], window_end_time)

# 找到对应的时间索引
window_mask = (t >= window_start_time) & (t <= window_end_time)
window_t = t[window_mask]
window_x = x[window_mask]

print(f"窗口: {window_start_time*1000:.6f}-{window_end_time*1000:.6f}ms, 数据点: {len(window_x)}")

if len(window_x) > 0:
    # 应用窗函数
    window_func = window_functions[selected_window](len(window_x))
    windowed_signal = window_x * window_func
    
    # 计算窗函数的功率补偿因子 - 修正计算方法
    # 正确计算: 振幅补偿因子 = 窗函数点数 / 窗函数之和
    amplitude_correction = len(window_func) / np.sum(window_func)
    # 功率补偿因子是振幅补偿因子的平方
    window_power_correction = amplitude_correction ** 2
    
    # 计算局部FFT
    window_fft = fft(windowed_signal)
    window_freqs = fftfreq(len(windowed_signal), 1/fs)
    
    # 计算振幅谱 - 直接使用振幅补偿因子，而非功率补偿因子
    window_amplitude = np.abs(window_fft) / len(windowed_signal) * amplitude_correction
    
    # 只保留正频率部分
    window_pos_mask = window_freqs > 0
    window_pos_freqs = window_freqs[window_pos_mask]
    window_pos_amplitude = window_amplitude[window_pos_mask]
    
    # 使用单边谱处理 - 乘以2是为了保持能量，除了直流和奈奎斯特频率外的所有成分都乘以2
    if len(window_pos_amplitude) > 1:
        window_pos_amplitude[1:] = window_pos_amplitude[1:] * 2  # 修正：所有正频率都乘以2（除0频率外）
    
    # 为每个主要频率找到FFT峰值
    for freq in main_freqs:
        freq_search_mask = (window_pos_freqs >= freq - search_width) & (window_pos_freqs <= freq + search_width)
        if np.any(freq_search_mask):
            search_freqs_local = window_pos_freqs[freq_search_mask]
            search_mags_local = window_pos_amplitude[freq_search_mask]
            
            # 在搜索范围内找到最大幅值
            peak_idx = np.argmax(search_mags_local)
            fft_peak_freq = search_freqs_local[peak_idx]
            fft_peak_mag = search_mags_local[peak_idx]
            
            # 打印详细的幅值计算信息，便于调试
            print(f"  FFT幅值计算详情:")
            
            # 找到完整FFT中对应的索引
            # 注意：这只是一个近似值，用于调试，因为窗口内的FFT索引与search_freqs_local的索引不同
            full_fft_idx = np.argmin(np.abs(window_freqs - fft_peak_freq))
            
            print(f"    峰值频率: {fft_peak_freq:.3f}Hz (找到的索引: {full_fft_idx})")
            print(f"    原始FFT幅值: {np.abs(window_fft[full_fft_idx]) / len(window_fft):.6e}")
            print(f"    振幅补偿因子: {amplitude_correction:.6f}")
            print(f"    单边谱乘数: 2.0")
            print(f"    最终FFT幅值: {fft_peak_mag:.6e}")
            
            # 计算频率分辨率
            freq_resolution = fs / len(windowed_signal)
            
            print(f"  动态窗口FFT分析:")
            print(f"    窗函数: {selected_window}")
            print(f"    功率补偿因子: {window_power_correction:.6f}")
            print(f"    频率分辨率: {freq_resolution:.6f}Hz")
            print(f"    FFT峰值: {fft_peak_mag:.6e} ({fft_peak_freq:.6f}Hz)")
            
            # 存储结果到fft_peaks_8_5_9_5
            fft_peaks_8_5_9_5[freq] = {
                'freq': fft_peak_freq,
                'mag': fft_peak_mag,
                'window_data': {
                    't': window_t,
                    'x': window_x,
                    'time_range': (window_start_time, window_end_time)  # 存储时间范围而不是掩码
                }
            }
            
            # 获取小波变换在同一窗口内的最大幅值
            # 错误: 不应该直接在原始信号window_x中查找峰值
            # 正确: 应该获取对应窗口内的小波变换结果并查找峰值
            # 因为在这个阶段还未进行小波变换，所以临时存储窗口信息，等小波变换后再处理
            wavelet_peaks_8_5_9_5[freq] = {
                'actual_freq': freq,
                'max_amp': 0,
                'max_time': 0
            }
            
            print(f"  动态窗口信息已保存，等待小波变换后处理")
        else:
            print(f"  在动态窗口内未找到目标频率附近的峰值")
            fft_peaks_8_5_9_5[freq] = {'freq': freq, 'mag': 0}
            wavelet_peaks_8_5_9_5[freq] = {'actual_freq': freq, 'max_amp': 0, 'max_time': 0}
else:
    print("错误：扩展时间窗口内没有数据")
    fft_peaks_8_5_9_5 = {freq: {'freq': 0, 'mag': 0} for freq in main_freqs}

# =========================================
# 步骤2: 在8.5ms到9.5ms时间段进行小波变换分析
# =========================================
print("\n=== 小波变换分析 ===")

# 使用复Morlet小波
central_freq = pywt.central_frequency(wavelet)

# 先对全部数据进行小波变换
wavelet_results = {}
wavelet_peaks_full = {}

for freq in main_freqs:
    # 计算对应的尺度
    scale = (central_freq * fs) / freq
    
    print(f"\n目标频率 {freq:.6f}Hz:")
    print(f"  计算尺度: {scale:.6f}")
    
    # 对完整信号进行小波变换
    wavelet_compute_start = time.time()
    try:
        coeffs, frequencies = pywt.cwt(x, [scale], wavelet, sampling_period=1/fs, method='fft')
        wavelet_compute_end = time.time()
        wavelet_compute_time = wavelet_compute_end - wavelet_compute_start
        pure_compute_time += wavelet_compute_time
        
        actual_freq = frequencies[0]
        amplitude = np.abs(coeffs[0])
        
        # 检查频率精度
        freq_error = abs(actual_freq - freq) / freq * 100
        print(f"  实际频率: {actual_freq:.6f}Hz (误差: {freq_error:.3f}%)")
        
        if freq_error > 1.0:  # 如果误差超过1%
            print(f"  警告：频率误差过大 ({freq_error:.3f}%)，可能影响分析精度")
            
        # 检查小波变换结果
        if np.all(amplitude == 0):
            print(f"  错误：小波变换结果全为零")
            continue
        elif np.any(np.isnan(amplitude)) or np.any(np.isinf(amplitude)):
            print(f"  错误：小波变换结果包含NaN或Inf值")
            continue
            
    except Exception as e:
        print(f"  错误：小波变换计算失败 - {str(e)}")
        continue
    
    # 步骤3: 在全部数据中找最大幅值（排除边缘2%）
    data_len = len(amplitude)
    edge_size = int(data_len * edge_exclude_ratio)
    center_start = edge_size
    center_end = data_len - edge_size
    
    # 计算有效时间范围（排除边缘后）
    valid_start_time = t[center_start]
    valid_end_time = t[center_end-1]
    
    center_data = amplitude[center_start:center_end]
    max_amp_idx_center = np.argmax(center_data) + center_start
    max_amp_time = t[max_amp_idx_center]
    max_amp_value = amplitude[max_amp_idx_center]
    
    wavelet_peaks_full[freq] = {
        'actual_freq': actual_freq,
        'max_amp': max_amp_value,
        'max_time': max_amp_time,
        'max_idx': max_amp_idx_center
    }
    
    print(f"{freq:.6f}Hz (实际: {actual_freq:.6f}Hz):")
    print(f"  全部数据峰值: {max_amp_value:.6e} {max_amp_time*1000:.6f}ms")
    
    # 修改: 使用基于小波变换最大幅值点的动态窗口
    # 以最大幅值点为中心，定义±1ms的时间窗口
    dynamic_window_start_time = max_amp_time - window_half_width_extended
    dynamic_window_end_time = max_amp_time + window_half_width_extended
    
    # 确保窗口在有效区域内
    dynamic_window_start_time = max(valid_start_time, dynamic_window_start_time)
    dynamic_window_end_time = min(valid_end_time, dynamic_window_end_time)
    
    print(f"\n=== 动态扩展窗口详细信息 ===")
    print(f"  小波峰值位置: {max_amp_time*1000:.3f}ms")
    print(f"  有效区域: {valid_start_time*1000:.3f}ms - {valid_end_time*1000:.3f}ms")
    print(f"  动态窗口: {dynamic_window_start_time*1000:.3f}ms - {dynamic_window_end_time*1000:.3f}ms")
    print(f"  窗口宽度: {(dynamic_window_end_time - dynamic_window_start_time)*1000:.3f}ms")
    
    # 找到对应的时间索引范围
    dynamic_window_mask = (t >= dynamic_window_start_time) & (t <= dynamic_window_end_time)
    if np.any(dynamic_window_mask):
        window_t = t[dynamic_window_mask]
        window_x = x[dynamic_window_mask]
        
        print(f"  窗口数据点数: {len(window_x)}")
        
        # 应用窗函数
        window_func = window_functions[selected_window](len(window_x))
        windowed_signal = window_x * window_func
        
        # 计算窗函数的功率补偿因子 - 修正计算方法
        # 正确计算: 振幅补偿因子 = 窗函数点数 / 窗函数之和
        amplitude_correction = len(window_func) / np.sum(window_func)
        # 功率补偿因子是振幅补偿因子的平方
        window_power_correction = amplitude_correction ** 2
        
        # 计算局部FFT
        window_fft = fft(windowed_signal)
        window_freqs = fftfreq(len(windowed_signal), 1/fs)
        
        # 计算振幅谱 - 直接使用振幅补偿因子，而非功率补偿因子
        window_amplitude = np.abs(window_fft) / len(windowed_signal) * amplitude_correction
        
        # 只保留正频率部分
        window_pos_mask = window_freqs > 0
        window_pos_freqs = window_freqs[window_pos_mask]
        window_pos_amplitude = window_amplitude[window_pos_mask]
        
        # 使用单边谱处理 - 乘以2是为了保持能量，除了直流和奈奎斯特频率外的所有成分都乘以2
        if len(window_pos_amplitude) > 1:
            window_pos_amplitude[1:] = window_pos_amplitude[1:] * 2  # 修正：所有正频率都乘以2（除0频率外）
        
        # 在目标频率附近搜索峰值
        freq_search_mask = (window_pos_freqs >= freq - search_width) & (window_pos_freqs <= freq + search_width)
        if np.any(freq_search_mask):
            search_freqs_local = window_pos_freqs[freq_search_mask]
            search_mags_local = window_pos_amplitude[freq_search_mask]
            
            # 在搜索范围内找到最大幅值
            peak_idx = np.argmax(search_mags_local)
            fft_peak_freq = search_freqs_local[peak_idx]
            fft_peak_mag = search_mags_local[peak_idx]
            
            # 打印详细的幅值计算信息，便于调试
            print(f"  FFT幅值计算详情:")
            
            # 找到完整FFT中对应的索引
            # 注意：这只是一个近似值，用于调试，因为窗口内的FFT索引与search_freqs_local的索引不同
            full_fft_idx = np.argmin(np.abs(window_freqs - fft_peak_freq))
            
            print(f"    峰值频率: {fft_peak_freq:.3f}Hz (找到的索引: {full_fft_idx})")
            print(f"    原始FFT幅值: {np.abs(window_fft[full_fft_idx]) / len(window_fft):.6e}")
            print(f"    振幅补偿因子: {amplitude_correction:.6f}")
            print(f"    单边谱乘数: 2.0")
            print(f"    最终FFT幅值: {fft_peak_mag:.6e}")
            
            # 计算频率分辨率
            freq_resolution = fs / len(windowed_signal)
            
            print(f"  动态窗口FFT分析:")
            print(f"    窗函数: {selected_window}")
            print(f"    功率补偿因子: {window_power_correction:.6f}")
            print(f"    频率分辨率: {freq_resolution:.6f}Hz")
            print(f"    FFT峰值: {fft_peak_mag:.6e} ({fft_peak_freq:.6f}Hz)")
            
            # 存储结果到fft_peaks_8_5_9_5
            fft_peaks_8_5_9_5[freq] = {
                'freq': fft_peak_freq,
                'mag': fft_peak_mag,
                'window_data': {
                    't': window_t,
                    'x': window_x,
                    'time_range': (dynamic_window_start_time, dynamic_window_end_time)  # 存储时间范围而不是掩码
                }
            }
            
            # 获取小波变换在同一窗口内的最大幅值
            # 错误: 不应该直接在原始信号window_x中查找峰值
            # 正确: 应该获取对应窗口内的小波变换结果并查找峰值
            # 因为在这个阶段还未进行小波变换，所以临时存储窗口信息，等小波变换后再处理
            wavelet_peaks_8_5_9_5[freq] = {
                'actual_freq': freq,
                'max_amp': 0,
                'max_time': 0
            }
            
            print(f"  动态窗口信息已保存，等待小波变换后处理")
        else:
            print(f"  在动态窗口内未找到目标频率附近的峰值")
            fft_peaks_8_5_9_5[freq] = {'freq': freq, 'mag': 0}
            wavelet_peaks_8_5_9_5[freq] = {'actual_freq': freq, 'max_amp': 0, 'max_time': 0}
    else:
        print(f"  动态窗口内无数据点")
        fft_peaks_8_5_9_5[freq] = {'freq': freq, 'mag': 0}
        wavelet_peaks_8_5_9_5[freq] = {'actual_freq': freq, 'max_amp': 0, 'max_time': 0}
    
    # 存储完整的小波变换结果用于后续归一化
    wavelet_results[actual_freq] = {
        'signal': np.real(coeffs[0]),
        'amplitude': amplitude,
        'target_freq': freq,
        'actual_freq': actual_freq,
        'input_scale': scale,
        'frequencies': frequencies
    }
    
    # 添加：处理之前保存的动态窗口数据，计算窗口内的小波峰值
    if freq in fft_peaks_8_5_9_5 and 'window_data' in fft_peaks_8_5_9_5[freq]:
        window_data = fft_peaks_8_5_9_5[freq]['window_data']
        window_time_range = window_data['time_range']
        
        # 根据时间范围创建掩码
        window_mask = (t >= window_time_range[0]) & (t <= window_time_range[1])
        
        # 提取窗口内的小波变换结果
        window_amplitude = amplitude[window_mask]
        window_t = t[window_mask]
        
        if len(window_amplitude) > 0:
            # 在窗口内找到最大幅值
            max_amp_idx_in_window = np.argmax(window_amplitude)
            max_amp_value_in_window = window_amplitude[max_amp_idx_in_window]
            max_amp_time_in_window = window_t[max_amp_idx_in_window]
            
            # 更新wavelet_peaks_8_5_9_5
            wavelet_peaks_8_5_9_5[freq] = {
                'actual_freq': actual_freq,
                'max_amp': max_amp_value_in_window,
                'max_time': max_amp_time_in_window
            }
            
            print(f"  动态窗口小波峰值: {max_amp_value_in_window:.6e} {max_amp_time_in_window*1000:.3f}ms")
        else:
            print(f"  动态窗口内无有效小波数据点")

# =========================================
# 步骤3: 对全部数据进行小波变换分析（去掉两端各2%）
# =========================================
print("\n=== 校准和归一化 ===")

# =========================================
# 步骤4: 通过比值计算FFT全部数据最大幅值并进行归一化
# =========================================

normalized_wavelet_results = {}
calibration_info = {}

for freq in main_freqs:
    # 检查该频率是否成功处理
    if freq not in wavelet_peaks_full:
        print(f"{freq:.6f}Hz: 小波变换失败，跳过校准")
        continue
        
    fft_peak_8_5_9_5_mag = fft_peaks_8_5_9_5[freq]['mag']
    wavelet_peak_8_5_9_5_mag = wavelet_peaks_8_5_9_5[freq]['max_amp']
    wavelet_peak_full_mag = wavelet_peaks_full[freq]['max_amp']
    actual_freq = wavelet_peaks_full[freq]['actual_freq']
    
    print(f"\n{freq:.6f}Hz校准检查:")
    print(f"  扩展窗口FFT峰值: {fft_peak_8_5_9_5_mag:.6e}")
    print(f"  扩展窗口小波峰值: {wavelet_peak_8_5_9_5_mag:.6e}")
    print(f"  全部数据小波峰值: {wavelet_peak_full_mag:.6e}")
    
    if fft_peak_8_5_9_5_mag > 0 and wavelet_peak_8_5_9_5_mag > 0:
        # 计算校准比值
        calibration_ratio = fft_peak_8_5_9_5_mag / wavelet_peak_8_5_9_5_mag
        
        # 推算FFT全部数据的峰值幅值
        estimated_fft_full_peak = wavelet_peak_full_mag * calibration_ratio
        
        # 计算归一化因子
        norm_factor = estimated_fft_full_peak / wavelet_peak_full_mag
        
        print(f"{freq:.6f}Hz校准:")
        print(f"  推算FFT峰值: {estimated_fft_full_peak:.6e}")
        
        # 验证合理性
        if wavelet_peak_full_mag < wavelet_peak_8_5_9_5_mag:
            print(f"  修正：使用扩展窗口峰值作为全部数据峰值")
            corrected_full_peak = wavelet_peak_8_5_9_5_mag
            estimated_fft_full_peak = corrected_full_peak * calibration_ratio
            norm_factor = estimated_fft_full_peak / corrected_full_peak
        
        # 对小波变换结果进行归一化
        wavelet_result = wavelet_results[actual_freq]
        normalized_signal = wavelet_result['signal'] * norm_factor
        normalized_amplitude = wavelet_result['amplitude'] * norm_factor
        
        normalized_wavelet_results[actual_freq] = {
            'signal': normalized_signal,
            'amplitude': normalized_amplitude,
            'norm_factor': norm_factor,
            'target_freq': freq,
            'actual_freq': actual_freq,
            'estimated_fft_peak': estimated_fft_full_peak
        }
        
        calibration_info[freq] = {
            'fft_peak_window': fft_peak_8_5_9_5_mag,
            'wavelet_peak_window': wavelet_peak_8_5_9_5_mag,
            'wavelet_peak_full': wavelet_peak_full_mag,
            'calibration_ratio': calibration_ratio,
            'estimated_fft_full': estimated_fft_full_peak,
            'norm_factor': norm_factor
        }
        
        # 保存归一化后的小波变换结果
        filename = f'wavelet_{actual_freq:.0f}Hz.txt'
        wavelet_data = np.column_stack((t, normalized_signal, normalized_amplitude))
        np.savetxt(os.path.join(result_folder, filename), wavelet_data,
                   header=f'Time(s) Signal_Normalized(target={freq:.2f}Hz,actual={actual_freq:.10f}Hz) Amplitude_Normalized',
                   fmt='%.8f %.8e %.8e',
                   delimiter=' ',
                   comments='')
    else:
        print(f"{freq:.6f}Hz校准失败：扩展窗口数据不足")
        calibration_info[freq] = {
            'fft_peak_window': fft_peak_8_5_9_5_mag,
            'wavelet_peak_window': wavelet_peak_8_5_9_5_mag,
            'wavelet_peak_full': wavelet_peak_full_mag,
            'calibration_ratio': 0,
            'estimated_fft_full': 0,
            'norm_factor': 1
        }

# =========================================
# 步骤5: 计算电压值 (从amplitude_to_us.py复制)
# =========================================
print("\n=== 电压值计算 ===")

# 直接使用固定的wavelet_470285Hz.txt文件
data_file = os.path.join(result_folder, 'wavelet_470285Hz.txt')
print(f"\n从固定文件计算电压值:")
print(f"  数据文件: {data_file}")
print(f"  校正因子: {correction_factor}")

try:
    # 直接读取已保存的数据
    wavelet_data = np.loadtxt(data_file, skiprows=1)  # 跳过标题行
    
    if wavelet_data.shape[1] >= 3:  # 确保有足够的列
        times = wavelet_data[:, 0]  # 第一列是时间
        amplitudes = wavelet_data[:, 2]  # 第三列是归一化幅值
        
        # 步骤1: 去掉两端数据
        print(f"\n步骤1: 去掉数据两端{trim_percent}%...")
        times_trimmed, amplitudes_trimmed = trim_data_edges(times, amplitudes, trim_percent)
        
        # 计算对应的Us值
        print("\n步骤2: 使用公式计算Us值 (含校正因子)...")
        Us_values_raw = calculate_us(amplitudes_trimmed, k_i, Q_i, epsilon_0, z_a, R, U_AC, correction_factor)
        
        # 额外计算不含校正因子的电压值
        Us_values_raw_no_correction = calculate_us(amplitudes_trimmed, k_i, Q_i, epsilon_0, z_a, R, U_AC, 1.0)
        
        # 保存原始电压数据（去掉两端后，未平滑）
        voltage_data_raw = np.column_stack((times_trimmed, Us_values_raw))
        output_file_raw = os.path.join(result_folder, "us_vs_time.txt")
        np.savetxt(output_file_raw, voltage_data_raw, fmt='%.8e', 
                  header='Time(s) Us_Original(V)', comments='')
        print(f"原始电压数据已保存到: {output_file_raw}")
        
        # 保存不含校正因子的原始电压数据
        voltage_data_raw_no_correction = np.column_stack((times_trimmed, Us_values_raw_no_correction))
        output_file_raw_no_correction = os.path.join(result_folder, "us_vs_time_no_correction.txt")
        np.savetxt(output_file_raw_no_correction, voltage_data_raw_no_correction, fmt='%.8e', 
                  header='Time(s) Us_Original_No_Correction(V)', comments='')
        print(f"无校正因子的原始电压数据已保存到: {output_file_raw_no_correction}")
        
        # 步骤3: 每组取平均值平滑处理
        print(f"\n步骤3: 每{points_per_group}个点取平均值平滑处理...")
        times_smoothed, Us_values_smoothed = smooth_by_average(
            times_trimmed, Us_values_raw, points_per_group
        )
        
        # 对无校正因子的电压值进行平滑处理
        _, Us_values_smoothed_no_correction = smooth_by_average(
            times_trimmed, Us_values_raw_no_correction, points_per_group
        )
        
        # 保存平滑后的电压数据
        voltage_data_smoothed = np.column_stack((times_smoothed, Us_values_smoothed))
        output_file_smoothed = os.path.join(result_folder, "us_vs_time_smoothed.txt")
        np.savetxt(output_file_smoothed, voltage_data_smoothed, fmt='%.8e', 
                  header='Time(s) Us_Smoothed(V)', comments='')
        print(f"平滑后电压数据已保存到: {output_file_smoothed}")
        
        # 保存无校正因子的平滑电压数据
        voltage_data_smoothed_no_correction = np.column_stack((times_smoothed, Us_values_smoothed_no_correction))
        output_file_smoothed_no_correction = os.path.join(result_folder, "us_vs_time_smoothed_no_correction.txt")
        np.savetxt(output_file_smoothed_no_correction, voltage_data_smoothed_no_correction, fmt='%.8e', 
                  header='Time(s) Us_Smoothed_No_Correction(V)', comments='')
        print(f"无校正因子的平滑电压数据已保存到: {output_file_smoothed_no_correction}")
        
        # 输出统计信息
        print(f"\n处理统计信息:")
        print(f"原始数据点数: {len(times)}")
        print(f"去掉两端后数据点数: {len(times_trimmed)}")
        print(f"平滑后数据点数: {len(times_smoothed)}")
        print(f"原始时间范围: {times_trimmed[0]:.6e} 到 {times_trimmed[-1]:.6e} s")
        print(f"平滑后时间范围: {times_smoothed[0]:.6e} 到 {times_smoothed[-1]:.6e} s")
        print(f"原始Us范围: {np.min(Us_values_raw):.6e} 到 {np.max(Us_values_raw):.6e} V")
        print(f"平滑后Us范围: {np.min(Us_values_smoothed):.6e} 到 {np.max(Us_values_smoothed):.6e} V")
        print(f"平滑方法: 每{points_per_group}个点取平均值")
        
        print(f"数据处理: 去掉两端{trim_percent}%, 每{points_per_group}个点取平均值")
        
    else:
        print(f"错误：数据文件格式不正确，列数不足")
        
except Exception as e:
    print(f"电压计算失败: {str(e)}")

# 计算总运行时间
total_end_time = time.time()
total_elapsed_time = total_end_time - total_start_time
io_time = total_elapsed_time - pure_compute_time

print(f"\n=== 完成 ===")
print(f"数据已保存到文件夹: {result_folder}")
print(f"总耗时: {total_elapsed_time:.6f}s (计算: {pure_compute_time:.6f}s, IO: {io_time:.6f}s)")

# 列出所有输出文件
print(f"\n输出文件列表:")
print(f"1. frequency_info.txt - 主要频率信息")
file_counter = 2
for freq in main_freqs:
    if freq in wavelet_peaks_full:
        actual_freq = wavelet_peaks_full[freq]['actual_freq']
        print(f"{file_counter}. wavelet_{actual_freq:.0f}Hz.txt - {freq:.0f}Hz归一化小波结果")
        file_counter += 1

# 列出电压结果文件
print(f"{file_counter}. us_vs_time.txt - 原始电压数据(含校正因子)")
file_counter += 1
print(f"{file_counter}. us_vs_time_smoothed.txt - 平滑后电压数据(含校正因子)")
file_counter += 1
print(f"{file_counter}. us_vs_time_no_correction.txt - 原始电压数据(无校正因子)")
file_counter += 1
print(f"{file_counter}. us_vs_time_smoothed_no_correction.txt - 平滑后电压数据(无校正因子)")
