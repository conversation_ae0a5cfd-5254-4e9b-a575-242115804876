% 加载提取后的数据
load('extracted_data3.mat');

tic;
Fs = 1e8; % 设定采样率
figure;

% 执行小波变换
cwt_modify(extracted_data3, Fs); % 进行小波变换

% 设置 x 轴刻度和标签
AX = gca;
xticklabels({'9', '9.5', '10', '10.5', '11', '11.5', '12', '12.5', '13'}); % 设置 x 轴标签
AX.YTick = 0:10:500; % 设置 y 轴刻度值，从 0 到 500，每隔 50
yticklabels(arrayfun(@num2str, 0:10:500, 'UniformOutput', false)); % 设置 y 轴标签

% 结束计时
elapsedTime = toc;

% 输出运行时间
fprintf('脚本运行时间: %.2f 秒\n', elapsedTime);



% % cwt(Amp, Fs);
% % 
% AX = gca;
% AX.YTickMode = "auto";
% AX.YTick = 2.^(-11:5);
% yticklabels({'-11', '-10', '-9', '-8', '-7', '-6', '-5', '-4', '-3', '-2', '-1', '0', '1', '2', '3', '4', '5'});
% % % xticklabels({'9', '9.5', '10', '10.5', '11', '11.5', '12', '12.5', '13'});








