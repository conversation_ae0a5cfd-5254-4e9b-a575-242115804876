% 设定采样率
Fs = 1e8; % 采样率 100,000,000 Hz

% 设定时间区间
start_time = 8e-3; % 8毫秒
end_time = 15e-3; % 9毫秒

% 计算对应的采样点索引
start_index = round(start_time * Fs);
end_index = round(end_time * Fs);

% 确保索引在信号范围内
if end_index > length(yout)
    end_index = length(yout);
end

% 提取数据
data20 = yout(start_index:end_index);

% 确保提取数据是列向量
data20 = data20(:);

% 生成对应时间轴（注意起点是 start_time）
time_axis = (start_index:end_index).' / Fs; % 保证和数据长度一致

% 检查维度
disp(size(time_axis));
disp(size(data20));

% 合并时间轴和数据，形成两列
if length(time_axis) == length(data20)
    output_data = [time_axis data20];
else
    error('时间轴和数据长度不一致！');
end

% 保存提取的数据到txt（两列，制表符分隔）
writematrix(output_data, 'data20.txt', 'Delimiter','tab');

% 绘制提取的数据
figure;
plot(time_axis, data20);
xlabel('时间 (秒)');
ylabel('信号幅度');
title('提取的信号数据 (8到15毫秒)');
grid on;
