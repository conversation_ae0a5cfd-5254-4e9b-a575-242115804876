import numpy as np
from scipy.fft import fft, fftfreq
import matplotlib
import pywt
import os
import time
import sys

# 设置结果路径变量
RESULT_ROOT = 'result_cwtnew'  # 这个变量可以被其他脚本导入使用

# 配置matplotlib支持中文显示
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'SimSun', 'FangSong']
matplotlib.rcParams['axes.unicode_minus'] = False

# 获取输入文件名
if len(sys.argv) > 1:
    input_filename = sys.argv[1]
else:
    input_filename = 'data10.txt'  # 默认文件名

# 提取文件基本名（不含扩展名）
file_base_name = os.path.splitext(os.path.basename(input_filename))[0]

# 开始记录总运行时间
total_start_time = time.time()
# 初始化纯计算时间计数器
pure_compute_time = 0

# 创建结果文件夹
result_folder = os.path.join(RESULT_ROOT, file_base_name)
os.makedirs(result_folder, exist_ok=True)

# 读取数据文件
data = np.loadtxt(input_filename)
t = data[:, 0]  # 第一列为时间
x = data[:, 1]  # 第二列为信号

# 添加去均值处理
x = x - np.mean(x)

# 打印原始信号的统计信息
print("\n原始信号统计信息:")
print(f"文件名: {input_filename}")
print(f"最大值: {np.max(x):.2e}")
print(f"最小值: {np.min(x):.2e}")
print(f"均方根: {np.sqrt(np.mean(x ** 2)):.2e}")

# 设置采样率
fs = 100e6  # 100MHz采样率

# 计算实际的时间步长
dt = t[1] - t[0]
print(f"数据时间步长: {dt} 秒")
print(f"原始数据点数: {len(t)}")

# =========================================
# 对全部数据进行FFT分析获取主要频率
# =========================================
print("\n开始对全部数据进行FFT分析...")

# 记录FFT计算开始时间
fft_compute_start = time.time()

# 应用汉宁窗以减少频谱泄漏
window = np.hanning(len(x))
windowed_signal = x * window

# 计算全部数据的FFT
X_full = fft(windowed_signal)
freqs_full = fftfreq(len(x), 1 / fs)
magnitude_full = np.abs(X_full) / len(x)

# 只保留正频率部分
positive_freq_mask_full = freqs_full > 0
pos_freqs_full = freqs_full[positive_freq_mask_full]
pos_magnitude_full = magnitude_full[positive_freq_mask_full]

# 对正频率部分乘以2（单边谱）
pos_magnitude_full = pos_magnitude_full * 2

# 记录FFT计算结束时间
fft_compute_end = time.time()
fft_compute_time = fft_compute_end - fft_compute_start
pure_compute_time += fft_compute_time
print(f"全部数据FFT纯计算耗时: {fft_compute_time:.4f} 秒")

# 保存全部数据的FFT分析结果
fft_results_full = np.column_stack((pos_freqs_full, pos_magnitude_full))
np.savetxt(os.path.join(result_folder, 'fft_results_full_data.txt'), fft_results_full,
           header='Frequency(Hz) Magnitude',
           fmt='%.2f %.6e',
           delimiter=' ',
           comments='')

# 查找并输出幅值最大的前10个频率
print("\n全部数据FFT分析 - 幅值最大的前10个频率:")
print("排名    频率(Hz)      幅值         相对幅值(%)")
print("-" * 50)

# 按幅值大小排序
sorted_indices = np.argsort(pos_magnitude_full)[::-1]  # 降序排列
top_indices = sorted_indices[:10]  # 取前10个
max_magnitude = pos_magnitude_full[sorted_indices[0]]  # 最大幅值

# 输出前10个频率和幅值
for i, idx in enumerate(top_indices):
    freq = pos_freqs_full[idx]
    magnitude = pos_magnitude_full[idx]
    relative_mag = (magnitude / max_magnitude) * 100  # 相对于最大幅值的百分比
    print(f"{i+1:2d}    {freq:10.2f}   {magnitude:8.2e}   {relative_mag:8.2f}%")

# 保存前10个频率的结果
top10_results = np.column_stack((
    np.arange(1, 11),  # 排名
    pos_freqs_full[top_indices],  # 频率
    pos_magnitude_full[top_indices],  # 幅值
    (pos_magnitude_full[top_indices] / max_magnitude) * 100  # 相对幅值(%)
))
np.savetxt(os.path.join(result_folder, 'top10_frequencies.txt'), top10_results,
           header='Rank Frequency(Hz) Magnitude Relative_Magnitude(%)',
           fmt='%2d %10.2f %8.2e %8.2f',
           delimiter=' ',
           comments='')

# 直接找到第一个（最大）峰值
first_peak_idx_full = np.argmax(pos_magnitude_full)
first_freq_full = pos_freqs_full[first_peak_idx_full]
first_mag_full = pos_magnitude_full[first_peak_idx_full]

# 计算预期的第二个频率（约为第一个频率的6.27倍）
expected_second_freq_full = first_freq_full * 6.27
search_width = 2000  # 搜索范围：目标频率±2000Hz

# 在预期频率附近搜索第二个峰值
freq_mask_full = (pos_freqs_full >= expected_second_freq_full - search_width) & (
            pos_freqs_full <= expected_second_freq_full + search_width)
search_freqs_full = pos_freqs_full[freq_mask_full]
search_mags_full = pos_magnitude_full[freq_mask_full]

# 检查是否找到第二个峰值
if len(search_mags_full) > 0:
    # 在搜索范围内找到最大幅值
    second_peak_idx_full = np.argmax(search_mags_full)
    second_freq_full = search_freqs_full[second_peak_idx_full]
    second_mag_full = search_mags_full[second_peak_idx_full]

    # 将频率结果存储在main_freqs和main_mags中
    main_freqs = np.array([first_freq_full, second_freq_full])
    main_mags = np.array([first_mag_full, second_mag_full])

    print("\n检测到的主要频率成分(基于全部数据):")
    print("频率(Hz)    幅值        频率比")
    print("-" * 40)
    print(f"{main_freqs[0]:8.2f}  {main_mags[0]:8.2e}  (基准频率)")
    print(f"{main_freqs[1]:8.2f}  {main_mags[1]:8.2e}  {main_freqs[1] / main_freqs[0]:8.3f}")
else:
    print(f"\n警告：在预期频率 {expected_second_freq_full:.2f}Hz ±{search_width}Hz 范围内未找到第二个峰值")
    print("将只分析第一个峰值")
    main_freqs = np.array([first_freq_full])
    main_mags = np.array([first_mag_full])

# =========================================
# 选择8-9ms范围内的数据进行FFT分析
# =========================================
print("\n选择8-9ms范围内的数据进行FFT分析...")
time_mask = (t >= 8e-3) & (t <= 9e-3)
t_selected = t[time_mask]
x_selected = x[time_mask]

print(f"选定范围内的数据点数: {len(t_selected)}")
print(f"选定范围内的时间范围: {t_selected[0]*1000:.3f}ms 到 {t_selected[-1]*1000:.3f}ms")
selected_max_value = np.max(np.abs(x_selected))
print(f"8-9ms区间信号最大振幅(原始): {selected_max_value:.4e}")

# =========================================
# FFT分析8-9ms范围的数据
# =========================================
print("\n开始FFT分析8-9ms范围的数据...")

# 记录FFT计算开始时间
fft_selected_start = time.time()

# 应用汉宁窗以减少频谱泄漏
window_selected = np.hanning(len(x_selected))
windowed_signal_selected = x_selected * window_selected

# 计算FFT
X_selected = fft(windowed_signal_selected)
freqs_selected = fftfreq(len(x_selected), 1 / fs)
magnitude_selected = np.abs(X_selected) / len(x_selected)

# 只保留正频率部分
positive_freq_mask_selected = freqs_selected > 0
pos_freqs_selected = freqs_selected[positive_freq_mask_selected]
pos_magnitude_selected = magnitude_selected[positive_freq_mask_selected]

# 对正频率部分乘以2（单边谱）
pos_magnitude_selected = pos_magnitude_selected * 2

# 记录FFT计算结束时间
fft_selected_end = time.time()
fft_selected_time = fft_selected_end - fft_selected_start
pure_compute_time += fft_selected_time
print(f"8-9ms数据FFT纯计算耗时: {fft_selected_time:.4f} 秒")

# 保存FFT分析结果
fft_results_selected = np.column_stack((pos_freqs_selected, pos_magnitude_selected))
np.savetxt(os.path.join(result_folder, 'fft_results_8to9ms.txt'), fft_results_selected,
           header='Frequency(Hz) Magnitude',
           fmt='%.2f %.6e',
           delimiter=' ',
           comments='')

# 直接找到第一个（最大）峰值
first_peak_idx_selected = np.argmax(pos_magnitude_selected)
first_freq_selected = pos_freqs_selected[first_peak_idx_selected]
first_mag_selected = pos_magnitude_selected[first_peak_idx_selected]

# 计算预期的第二个频率（约为第一个频率的6.27倍）
expected_second_freq_selected = first_freq_selected * 6.27
search_width_selected = 2000  # 搜索范围：目标频率±2000Hz

# 在预期频率附近搜索第二个峰值
freq_mask_selected = (pos_freqs_selected >= expected_second_freq_selected - search_width_selected) & (
            pos_freqs_selected <= expected_second_freq_selected + search_width_selected)
search_freqs_selected = pos_freqs_selected[freq_mask_selected]
search_mags_selected = pos_magnitude_selected[freq_mask_selected]

# 检查是否找到第二个峰值
if len(search_mags_selected) > 0:
    # 在搜索范围内找到最大幅值
    second_peak_idx_selected = np.argmax(search_mags_selected)
    second_freq_selected = search_freqs_selected[second_peak_idx_selected]
    second_mag_selected = search_mags_selected[second_peak_idx_selected]

    # 将频率结果存储在selected_freqs和selected_mags中
    selected_freqs = np.array([first_freq_selected, second_freq_selected])
    selected_mags = np.array([first_mag_selected, second_mag_selected])

    print("\n检测到的主要频率成分(基于8-9ms数据):")
    print("频率(Hz)    幅值        频率比")
    print("-" * 40)
    print(f"{selected_freqs[0]:8.2f}  {selected_mags[0]:8.2e}  (基准频率)")
    print(f"{selected_freqs[1]:8.2f}  {selected_mags[1]:8.2e}  {selected_freqs[1] / selected_freqs[0]:8.3f}")
else:
    print(f"\n警告：在预期频率 {expected_second_freq_selected:.2f}Hz ±{search_width_selected}Hz 范围内未找到第二个峰值")
    print("将只分析第一个峰值")
    selected_freqs = np.array([first_freq_selected])
    selected_mags = np.array([first_mag_selected])

# =========================================
# 比较全部数据与8-9ms数据的分析结果
# =========================================
print("\n比较全部数据与8-9ms数据的分析结果:")
print("频率(Hz)     全部数据幅值   8-9ms数据幅值   幅值比例")
print("-" * 60)
for i in range(min(len(main_freqs), len(selected_freqs))):
    ratio = selected_mags[i] / main_mags[i] if main_mags[i] != 0 else float('inf')
    print(f"{main_freqs[i]:8.2f}   {main_mags[i]:8.2e}     {selected_mags[i]:8.2e}    {ratio:8.2f}")

# 保存比较结果到文件
comparison_file = os.path.join(result_folder, 'frequency_comparison.txt')
with open(comparison_file, 'w', encoding='utf-8') as f:
    f.write("全局分析与8-9ms数据分析比较:\n")
    f.write("频率(Hz)     全部数据幅值   8-9ms数据幅值   幅值比例\n")
    f.write("-" * 60 + "\n")
    for i in range(min(len(main_freqs), len(selected_freqs))):
        ratio = selected_mags[i] / main_mags[i] if main_mags[i] != 0 else float('inf')
        f.write(f"{main_freqs[i]:8.2f}   {main_mags[i]:8.2e}     {selected_mags[i]:8.2e}    {ratio:8.2f}\n")

# =========================================
# 使用小波变换分析所有检测到的频率成分（应用到全部信号）
# =========================================
print(f"\n开始小波变换分析所有检测到的频率成分（全部数据）...")

# 记录小波变换开始时间
pywt_start_time = time.time()

# 使用复Morlet小波
wavelet = 'cmor3.0-1.0'
central_freq = pywt.central_frequency(wavelet)
print(f"小波 {wavelet} 的中心频率: {central_freq}")

# 为每个主要频率计算对应的尺度
scales = []
for freq in main_freqs:
    scale = (central_freq * fs) / freq
    scales.append(scale)
    print(f"目标频率 {freq:.2f}Hz 对应的尺度: {scale:.4f}")

# 设置边缘排除比例 - 在信号两端各排除这个比例的数据
edge_exclude_ratio = 0.10  # 排除信号两端各10%的数据

# 设置FFT分析窗口参数 - 根据频率动态调整窗口大小
def get_window_size(freq):
    # 窗口至少包含目标频率的10个周期
    min_cycles = 10
    if freq < 100000:  # 低频成分需要更多周期数
        min_cycles = 20
    window_seconds = min_cycles / freq
    # 但不小于500微秒，不大于5毫秒
    window_seconds = max(1e-3, min(5e-3, window_seconds))
    window_points = int(window_seconds * fs)
    # 确保点数为偶数
    if window_points % 2 != 0:
        window_points += 1
    return window_points

# 对每个主要频率进行小波变换
wavelet_results = {}
actual_freqs = []  # 存储实际计算的频率

for i, (freq, scale) in enumerate(zip(main_freqs, scales)):
    # 计算该频率的窗口大小
    window_size_points = get_window_size(freq)
    window_size_seconds = window_size_points / fs
    print(f"\n频率 {freq:.2f}Hz 的FFT分析窗口大小: {window_size_points} 点 ({window_size_seconds*1e6:.1f} 微秒)")
    
    # 对完整信号进行小波变换（传入采样周期）
    wavelet_compute_start = time.time()
    sampling_period = 1/fs  # 采样周期 = 1/采样频率 = 1e-8 秒
    coeffs, _ = pywt.cwt(x, [scale], wavelet, method='fft', sampling_period=sampling_period)
    coeffs = coeffs[0]  # 取第一个尺度的结果

    wavelet_compute_end = time.time()
    wavelet_compute_time = wavelet_compute_end - wavelet_compute_start
    pure_compute_time += wavelet_compute_time

    # 计算实际频率值
    actual_freq = (central_freq * fs) / scale  # 直接从尺度计算频率
    actual_freqs.append(actual_freq)
    
    # 计算小波变换幅值
    amplitude = np.abs(coeffs)
    
    # 排除边缘效应区域
    data_len = len(amplitude)
    edge_size = int(data_len * edge_exclude_ratio)
    center_start = edge_size
    center_end = data_len - edge_size
    
    # 确保中间区域非空
    if center_end <= center_start:
        # 如果中间区域为空，使用整个数组
        center_data = amplitude
        print(f"  警告：数据长度 {data_len} 太短，无法排除边缘数据，使用全部数据计算小波幅值")
        center_start = 0
        center_end = data_len
    else:
        # 只使用中间部分数据
        center_data = amplitude[center_start:center_end]
    
    # 找到小波变换幅值最大的时间点
    max_amp_idx = np.argmax(center_data) + center_start  # 加上中心区域的起始偏移
    max_amp_time = t[max_amp_idx]
    max_amp_value = amplitude[max_amp_idx]
    
    print(f"频率 {actual_freq:.2f}Hz 小波变换最大幅值:")
    print(f"  最大幅值时间点: {max_amp_time:.8f} 秒 (索引: {max_amp_idx})")
    print(f"  最大幅值: {max_amp_value:.4e}")
    
    # =========================================
    # 在最大幅值时间点附近进行FFT分析
    # =========================================
    
    # 计算窗口的起始和结束位置
    half_window = window_size_points // 2
    window_start = max(0, max_amp_idx - half_window)
    window_end = min(len(x), max_amp_idx + half_window)
    
    # 提取窗口数据
    window_data = x[window_start:window_end]
    window_time = t[window_start:window_end]
    
    # 确保窗口内的时间点包含了最大幅值时间点
    print(f"  窗口时间范围: {window_time[0]:.8f} - {window_time[-1]:.8f} 秒")
    if window_time[0] <= max_amp_time <= window_time[-1]:
        print(f"  ✓ 窗口包含最大幅值时间点")
    else:
        print(f"  ✗ 警告: 窗口不包含最大幅值时间点!")
    
    # 应用汉宁窗减少频谱泄漏
    window_func = np.hanning(len(window_data))
    windowed_data = window_data * window_func
    
    # 对窗口数据进行FFT
    window_fft_start = time.time()
    X_window = fft(windowed_data)
    freqs_window = fftfreq(len(windowed_data), 1 / fs)
    magnitude_window = np.abs(X_window) / len(window_data)
    
    # 只保留正频率部分
    positive_freq_mask_window = freqs_window > 0
    pos_freqs_window = freqs_window[positive_freq_mask_window]
    pos_magnitude_window = magnitude_window[positive_freq_mask_window]

    # 对正频率部分乘以2（单边谱）
    pos_magnitude_window = pos_magnitude_window * 2

    window_fft_end = time.time()
    window_fft_time = window_fft_end - window_fft_start
    pure_compute_time += window_fft_time
    
    # 保存窗口FFT结果
    window_fft_results = np.column_stack((pos_freqs_window, pos_magnitude_window))
    window_fft_filename = f'window_fft_{actual_freq:.0f}Hz.txt'
    np.savetxt(os.path.join(result_folder, window_fft_filename), window_fft_results,
               header=f'Frequency(Hz) Magnitude(at_time={max_amp_time:.8f}s)',
               fmt='%.2f %.6e',
               delimiter=' ',
               comments='')
    
    # 在目标频率附近找最大值
    # 搜索范围:较低频率用±1000Hz，较高频率用±1%频率
    if freq < 100000:  # 低于100kHz
        search_width_local = 2000  # 固定±2000Hz
    else:
        search_width_local = freq * 0.01  # 频率的±1%
        
    print(f"  局部FFT频率搜索范围: {actual_freq - search_width_local:.2f} - {actual_freq + search_width_local:.2f} Hz")
    
    # 在目标频率附近找峰值
    freq_mask_local = (pos_freqs_window >= actual_freq - search_width_local) & (
                pos_freqs_window <= actual_freq + search_width_local)
                
    if np.any(freq_mask_local):  # 确保掩码包含至少一个值
        local_freqs = pos_freqs_window[freq_mask_local]
        local_mags = pos_magnitude_window[freq_mask_local]
        
        # 在目标频率附近找最大幅值
        local_peak_idx = np.argmax(local_mags)
        local_peak_freq = local_freqs[local_peak_idx]
        local_peak_mag = local_mags[local_peak_idx]
        
        # 计算频率分辨率
        freq_resolution = fs / len(window_data)
        print(f"  窗口频率分辨率: {freq_resolution:.2f} Hz")
        
        print(f"  最大幅值时间窗口的FFT分析:")
        print(f"    目标频率: {actual_freq:.2f}Hz")
        print(f"    检测到的峰值频率: {local_peak_freq:.2f}Hz (差异: {local_peak_freq - actual_freq:.2f}Hz)")
        print(f"    峰值幅度: {local_peak_mag:.4e}")
        print(f"    参考对比 - 全局FFT幅度: {main_mags[i]:.4e}")
        
        # 保存用于归一化的FFT幅值
        local_to_global_ratio = local_peak_mag / main_mags[i]
        print(f"    局部/全局幅值比: {local_to_global_ratio:.4f}")
        
        # 如果局部FFT幅值与全局FFT幅值差异过大，可能有问题
        if local_to_global_ratio > 3 or local_to_global_ratio < 0.33:
            print(f"    ⚠️ 警告: 局部FFT幅值与全局FFT幅值差异显著!")
            print(f"    使用修正后的局部FFT幅值...")
            # 使用全局FFT幅值与局部FFT幅值的几何平均值
            fft_norm_value = np.sqrt(local_peak_mag * main_mags[i])
            print(f"    修正后的幅值(几何平均): {fft_norm_value:.4e}")
        else:
            # 局部FFT幅值与全局FFT幅值比较接近，直接使用局部FFT幅值
            fft_norm_value = local_peak_mag
    else:
        print(f"  警告：在最大幅值时间窗口的FFT中未找到目标频率附近的峰值")
        # 如果未找到合适的峰值，使用全局FFT的结果
        fft_norm_value = main_mags[i]
        print(f"    使用全局FFT幅值: {fft_norm_value:.4e}")
    
    # 存储结果
    wavelet_results[actual_freq] = {
        'coeffs': coeffs,  # 原始小波变换系数
        'target_freq': freq,  # 目标频率
        'actual_freq': actual_freq,  # 实际计算的频率
        'input_scale': scale,  # 输入尺度
        'max_amp_idx': max_amp_idx,  # 最大幅值的索引
        'max_amp_time': max_amp_time,  # 最大幅值的时间
        'max_amp_value': max_amp_value,  # 小波变换最大幅值
        'fft_norm_value': fft_norm_value,  # 用于归一化的FFT幅值
        'window_time': window_time,  # 窗口时间范围
        'window_data': window_data,  # 窗口原始数据
        'global_fft_value': main_mags[i],  # 全局FFT幅值
    }

# 完成归一化计算
for i, actual_freq in enumerate(actual_freqs):
    result = wavelet_results[actual_freq]
    max_amp_value = result['max_amp_value']
    fft_norm_value = result['fft_norm_value']
    
    # 计算归一化因子
    norm_factor = fft_norm_value / max_amp_value
    
    # 对整个信号应用归一化因子
    wavelet_signal = np.real(result['coeffs']) * norm_factor
    amplitude = np.abs(result['coeffs']) * norm_factor
    
    # 更新结果
    wavelet_results[actual_freq].update({
        'signal': wavelet_signal,  # 归一化后的信号
        'amplitude': amplitude,  # 归一化后的幅值
        'norm_factor': norm_factor,  # 归一化因子
        'norm_reference': fft_norm_value,  # 归一化参考值（FFT幅值）
        'method_name': f"基于最大幅值点({result['max_amp_time']:.8f}s)的FFT幅值"
    })
    
    # 打印归一化信息
    print(f"\n频率分量 {i + 1} 归一化结果:")
    print(f"  目标频率: {result['target_freq']:.2f}Hz")
    print(f"  实际频率: {actual_freq:.10f}Hz")
    print(f"  最大幅值时间: {result['max_amp_time']:.8f}秒")
    print(f"  小波变换最大幅值: {max_amp_value:.4e}")
    print(f"  全局FFT幅值: {result['global_fft_value']:.4e}")
    print(f"  局部FFT幅值: {fft_norm_value:.4e}")
    print(f"  归一化因子: {norm_factor:.4f}")

# 保存小波变换的频率和幅值结果
wavelet_freq_amp_file = os.path.join(result_folder, 'wavelet_frequencies_amplitudes.txt')
with open(wavelet_freq_amp_file, 'w', encoding='utf-8') as f:
    f.write(
        "目标频率(Hz) 实际频率(Hz) 最大幅值时间(s) 小波最大幅值 局部FFT幅值 全局FFT幅值 归一化因子 频率差异(Hz) 频率差异(%)\n")
    f.write("-" * 160 + "\n")
    for actual_freq in actual_freqs:
        result = wavelet_results[actual_freq]
        target_freq = result['target_freq']
        freq_diff = actual_freq - target_freq
        freq_diff_percent = freq_diff / target_freq * 100 if target_freq != 0 else 0
        f.write(
            f"{target_freq:12.2f} {actual_freq:20.10f} {result['max_amp_time']:15.8f} {result['max_amp_value']:15.4e} {result['fft_norm_value']:15.4e} {result['global_fft_value']:15.4e} {result['norm_factor']:12.4f} {freq_diff:12.10f} {freq_diff_percent:10.5f}\n")

# 记录小波变换结束时间并计算总时间
pywt_end_time = time.time()
pywt_elapsed_time = pywt_end_time - pywt_start_time
print(f"\n小波变换总耗时: {pywt_elapsed_time:.4f} 秒")
print(f"其中纯计算耗时: {pure_compute_time - fft_compute_time:.4f} 秒")
print(f"其他操作耗时: {pywt_elapsed_time - (pure_compute_time - fft_compute_time):.4f} 秒")

# 打印分析结果
print("\n频率分量分析结果:")
for i, actual_freq in enumerate(actual_freqs):
    result = wavelet_results[actual_freq]
    target_freq = result['target_freq']
    print(f"\n{actual_freq:.0f}Hz 分量 (目标频率 {target_freq:.2f}Hz):")
    print(f"小波变换最大幅值时间: {result['max_amp_time']:.8f}秒")
    print(f"归一化方法: 基于最大幅值时间点的FFT幅值")
    print(f"归一化幅值: {result['norm_reference']:.4e}")
    print(f"全局FFT幅值: {result['global_fft_value']:.4e}")
    print(f"局部/全局FFT幅值比: {result['fft_norm_value']/result['global_fft_value']:.4f}")

# =========================================
# 保存分析结果到文件
# =========================================

# 保存主要频率信息
with open(os.path.join(result_folder, 'frequency_info.txt'), 'w', encoding='utf-8') as f:
    f.write(f"检测到的主要频率(基于{file_base_name}全部数据):\n")
    f.write("目标频率(Hz)    实际频率(Hz)        最大幅值时间(s)    局部FFT幅值   全局FFT幅值   比值\n")
    f.write("-" * 90 + "\n")
    for i, actual_freq in enumerate(actual_freqs):
        result = wavelet_results[actual_freq]
        target_freq = result['target_freq']
        ratio = result['fft_norm_value'] / result['global_fft_value']
        f.write(f"{target_freq:8.2f}  {actual_freq:20.10f}  {result['max_amp_time']:15.8f}  {result['fft_norm_value']:10.4e}  {result['global_fft_value']:10.4e}  {ratio:6.4f}\n")

# 保存原始信号和时间数据
signal_data = np.column_stack((t, x))
np.savetxt(os.path.join(result_folder, 'original_signal.txt'), signal_data,
           fmt='%.8f %.8e',
           delimiter=' ',
           comments='')

# 保存最大幅值点附近的窗口数据
for i, actual_freq in enumerate(actual_freqs):
    result = wavelet_results[actual_freq]
    window_filename = f'max_amplitude_window_{actual_freq:.0f}Hz.txt'
    window_data = np.column_stack((
        result['window_time'],
        result['window_data']
    ))
    np.savetxt(os.path.join(result_folder, window_filename), window_data,
               header=f'Time(s) Signal(window_around_max_amplitude_at_{result["max_amp_time"]:.8f}s)',
               fmt='%.8f %.8e',
               delimiter=' ',
               comments='')

# 保存小波变换结果
for i, actual_freq in enumerate(actual_freqs):
    filename = f'wavelet_{actual_freq:.0f}Hz.txt'
    wavelet_data = np.column_stack((
        t,
        wavelet_results[actual_freq]['signal'],
        wavelet_results[actual_freq]['amplitude']
    ))
    target_freq = wavelet_results[actual_freq]['target_freq']
    method_name = wavelet_results[actual_freq]['method_name']
    np.savetxt(os.path.join(result_folder, filename), wavelet_data,
               header=f'Time(s) Signal(target={target_freq:.2f}Hz,actual={actual_freq:.10f}Hz,norm={method_name}) Amplitude',
               fmt='%.8f %.8e %.8e',
               delimiter=' ',
               comments='')

# 保存8-9ms范围的主要频率信息
with open(os.path.join(result_folder, 'frequency_info_8to9ms.txt'), 'w', encoding='utf-8') as f:
    f.write("检测到的主要频率(基于8-9ms数据):\n")
    f.write("目标频率(Hz)    实际频率(Hz)        幅值\n")
    f.write("-" * 50 + "\n")
    for i, freq in enumerate(selected_freqs):
        f.write(f"{freq:8.2f}  {freq:20.10f}  {selected_mags[i]:8.4e}\n")

# 保存选定时间范围的原始信号和时间数据
signal_data_selected = np.column_stack((t_selected, x_selected))
np.savetxt(os.path.join(result_folder, 'selected_signal_8to9ms.txt'), signal_data_selected,
           fmt='%.8f %.8e',
           delimiter=' ',
           comments='')

# 计算总运行时间
total_end_time = time.time()
total_elapsed_time = total_end_time - total_start_time
io_time = total_elapsed_time - pure_compute_time

print(f"\n数据已保存到文件夹：{result_folder}")
print("包含以下文件：")
print(f"1. frequency_info.txt - 主要频率信息(基于全部数据)")
print(f"2. frequency_info_8to9ms.txt - 主要频率信息(基于8-9ms数据)")
print(f"3. frequency_comparison.txt - 全局与8-9ms频率比较")
print(f"4. wavelet_frequencies_amplitudes.txt - 小波变换详细信息")
print(f"5. fft_results_full_data.txt - 全部数据的FFT分析结果")
print(f"6. fft_results_8to9ms.txt - 8-9ms范围的FFT分析结果")
print(f"7. original_signal.txt - 原始信号数据")
print(f"8. selected_signal_8to9ms.txt - 8-9ms范围的信号数据")

# 打印性能分析结果
print("\n性能分析结果:")
print(f"总运行时间: {total_elapsed_time:.4f} 秒")
print(f"纯计算时间: {pure_compute_time:.4f} 秒 ({pure_compute_time / total_elapsed_time * 100:.1f}%)")
print(f"其他操作时间: {io_time:.4f} 秒 ({io_time / total_elapsed_time * 100:.1f}%)")
print("\n处理说明:")
print(f"- 处理文件: {input_filename}")
print("- 对全局数据应用汉宁窗后进行FFT分析获取主要频率")
print("- 使用小波变换找出各频率分量最大幅值的时间点")
print("- 对每个频率动态调整窗口大小，确保包含足够的周期数")
print("- 在最大幅值时间点附近进行局部FFT分析获取频率幅值")
print("- 将局部FFT与全局FFT结果进行比较，必要时进行修正")
print("- 使用局部FFT幅值对小波变换结果进行归一化")
print("- FFT频谱已包含×2修正（对非直流和Nyquist频率）")