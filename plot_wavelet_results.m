% 读取数据
data_name = 'data10';
data_folder = ['result_cwtanalyze_dynamic_all/' data_name, '/'];  % 从当前目录读取

% 定义要分析的两个频率文件
freq1_file = 'wavelet_75000Hz.txt';
freq2_file = 'wavelet_470285Hz.txt';
freq1_name = '75000Hz';
freq2_name = '470285Hz';

% 读取原始信号信息（用于时间轴）
original_data = importdata([data_folder, 'original_signal.txt']);
if isstruct(original_data)
    sample_idx = original_data.data(:,1);
    signal = original_data.data(:,2);
else
    sample_idx = original_data(:,1);
    signal = original_data(:,2);
end

% 将采样点转换为时间（毫秒）
fs = 100e6;  % 采样率100MHz
t_ms = sample_idx * 1000;  % 转换为毫秒

% 输出数据基本信息
disp(['数据点数: ' num2str(length(t_ms))]);
disp(['信号范围: ' num2str(min(signal), '%.2e') ' 到 ' num2str(max(signal), '%.2e')]);
disp(['时间范围: ' num2str(min(t_ms), '%.2e') ' 到 ' num2str(max(t_ms), '%.2e') ' 毫秒']);

% 读取第一个频率的小波分析结果
try
    wavelet_data1 = importdata([data_folder, freq1_file]);
    if isstruct(wavelet_data1)
        wavelet_data1 = wavelet_data1.data;
    end
    disp(['成功读取 ' freq1_name ' 小波分析结果']);
catch
    error(['无法读取文件: ' freq1_file]);
end

% 读取第二个频率的小波分析结果
try
    wavelet_data2 = importdata([data_folder, freq2_file]);
    if isstruct(wavelet_data2)
        wavelet_data2 = wavelet_data2.data;
    end
    disp(['成功读取 ' freq2_name ' 小波分析结果']);
catch
    error(['无法读取文件: ' freq2_file]);
end

% 创建时间轴（根据小波数据的长度）
t_wavelet1 = linspace(min(t_ms), max(t_ms), size(wavelet_data1, 1));
t_wavelet2 = linspace(min(t_ms), max(t_ms), size(wavelet_data2, 1));

% 提取振幅数据
amplitude1 = wavelet_data1(:,3);  % 第3列是振幅
amplitude2 = wavelet_data2(:,3);  % 第3列是振幅

% 创建第一个图形：74999Hz频率的振幅变化
figure('Position', [100 100 800 500]);
plot(t_wavelet1, amplitude1, 'r', 'LineWidth', 1.5);
title([data_name ' - ' freq1_name ' 频率振幅随时间变化']);
xlabel('时间 (ms)');
ylabel('振幅');
grid on;
set(gcf, 'Color', 'w');
set(gca, 'FontSize', 12);

% 创建第二个图形：469995Hz频率的振幅变化
figure('Position', [300 200 800 500]);
plot(t_wavelet2, amplitude2, 'b', 'LineWidth', 1.5);
title([data_name ' - ' freq2_name ' 频率振幅随时间变化']);
xlabel('时间 (ms)');
ylabel('振幅');
grid on;
set(gcf, 'Color', 'w');
set(gca, 'FontSize', 12);

% 输出两个频率的统计信息
disp(['=== ' freq1_name ' 频率分量统计 ===']);
disp(['平均振幅: ' num2str(mean(amplitude1), '%.2e')]);
disp(['最大振幅: ' num2str(max(amplitude1), '%.2e')]);
disp(['最小振幅: ' num2str(min(amplitude1), '%.2e')]);

disp(['=== ' freq2_name ' 频率分量统计 ===']);
disp(['平均振幅: ' num2str(mean(amplitude2), '%.2e')]);
disp(['最大振幅: ' num2str(max(amplitude2), '%.2e')]);
disp(['最小振幅: ' num2str(min(amplitude2), '%.2e')]); 