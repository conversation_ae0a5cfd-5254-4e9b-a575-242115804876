% 保存yout数据，并重命名为yout0
yout22 = yout1; % 确保 yout 变量已定义  % 每次都需要改 4
save('yout22.mat', 'yout22');  % 每次都需要改 3

%yout2.mat  2-1-2
%yout3.mat  2-1-1
%yout5.mat  2-1-2-1 周期1ms
%yout6.mat  2-1-2-1 周期2ms
%yout7.mat  2-1-2-1 周期2ms
%yout8.mat  2-1-2-1 周期2ms 采样率2M

%yout9.mat  2-1-2 专利 采样率100M
%yout10.mat  2-1-1 师兄论文 采样率100M
%yout11.mat  2-1-1 师兄论文 采样率100M  5v
%yout22.mat  2-1-1 师兄论文 采样率100M  3v  第二模态

% 设定采样率
Fs = 1e8; % 采样率 100,000,000 Hz    % 注意改采样率

% 设定时间区间
start_time = 9e-3; % 9毫秒
end_time = 13e-3; % 13毫秒

% 计算对应的采样点索引
start_index = round(start_time * Fs);
end_index = round(end_time * Fs);

% 确保索引在信号范围内
if end_index > length(yout3)
    end_index = length(yout3);
end

% 提取数据
extracted_data22 = yout1(start_index:end_index); % 每次都需要改 2

% 保存提取后的数据
save('extracted_data22.mat', 'extracted_data22'); % 保存提取的数据 % 每次都需要改 1

% 绘制提取的数据
figure;
plot((start_index:end_index) / Fs, extracted_data22); % 每次都需要改 0
xlabel('时间 (秒)');
ylabel('信号幅度');
title('提取的信号数据 (9到13毫秒)');
grid on;




