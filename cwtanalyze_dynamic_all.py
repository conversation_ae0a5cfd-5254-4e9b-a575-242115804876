import numpy as np
from scipy.fft import fft, fftfreq
import matplotlib
import pywt
import os
import time  # 添加time模块用于计时

# 配置matplotlib支持中文显示
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'SimSun', 'FangSong']
matplotlib.rcParams['axes.unicode_minus'] = False

# 开始记录总运行时间
total_start_time = time.time()
# 初始化纯计算时间计数器
pure_compute_time = 0

# 创建结果文件夹
input_filename = 'data10.txt'
result_folder = os.path.join('result_cwtanalyze_dynamic_all', os.path.splitext(input_filename)[0])
os.makedirs(result_folder, exist_ok=True)

# 归一化方法：fft计算最大振幅，cwt计算在8-9ms范围内的最大振幅，对cwt结果进行归一化

# 读取数据文件
data = np.loadtxt(input_filename)
t = data[:, 0]  # 第一列为时间
x = data[:, 1]  # 第二列为信号

# 添加去均值处理
x = x - np.mean(x)

# 打印原始信号的统计信息
print("\n原始信号统计信息:")
print(f"最大值: {np.max(x):.2e}")
print(f"最小值: {np.min(x):.2e}")
print(f"均方根: {np.sqrt(np.mean(x ** 2)):.2e}")

# 设置采样率
fs = 100e6  # 100MHz采样率

# 计算实际的时间步长
dt = t[1] - t[0]
print(f"数据时间步长: {dt} 秒")
print(f"原始数据点数: {len(t)}")

# =========================================
# 首先对全部数据进行FFT分析获取主要频率
# =========================================
print("\n开始对全部数据进行FFT分析...")

# 记录FFT计算开始时间
fft_compute_start = time.time()

# 计算全部数据的FFT
X_full = fft(x)
freqs_full = fftfreq(len(x), 1 / fs)
magnitude_full = np.abs(X_full) / len(x)

# 只保留正频率部分
positive_freq_mask_full = freqs_full > 0
pos_freqs_full = freqs_full[positive_freq_mask_full]
pos_magnitude_full = magnitude_full[positive_freq_mask_full]

# 修改频谱规范化方式
# 对中间频率(非直流和Nyquist)乘以2
if len(pos_magnitude_full) > 1:
    pos_magnitude_full[1:-1] = pos_magnitude_full[1:-1] * 2

# 记录FFT计算结束时间
fft_compute_end = time.time()
fft_compute_time = fft_compute_end - fft_compute_start
pure_compute_time += fft_compute_time
print(f"全部数据FFT纯计算耗时: {fft_compute_time:.4f} 秒")

# 保存全部数据的FFT分析结果
fft_results_full = np.column_stack((pos_freqs_full, pos_magnitude_full))
np.savetxt(os.path.join(result_folder, 'fft_results_full_data.txt'), fft_results_full,
           header='Frequency(Hz) Magnitude',
           fmt='%.2f %.6e',
           delimiter=' ',
           comments='')

# 直接找到第一个（最大）峰值
first_peak_idx_full = np.argmax(pos_magnitude_full)
first_freq_full = pos_freqs_full[first_peak_idx_full]
first_mag_full = pos_magnitude_full[first_peak_idx_full]

# 计算预期的第二个频率（约为第一个频率的6.27倍）
expected_second_freq_full = first_freq_full * 6.27
search_width = 2000  # 搜索范围：目标频率±2000Hz

# 在预期频率附近搜索第二个峰值
freq_mask_full = (pos_freqs_full >= expected_second_freq_full - search_width) & (pos_freqs_full <= expected_second_freq_full + search_width)
search_freqs_full = pos_freqs_full[freq_mask_full]
search_mags_full = pos_magnitude_full[freq_mask_full]

# 检查是否找到第二个峰值
if len(search_mags_full) > 0:
    # 在搜索范围内找到最大幅值
    second_peak_idx_full = np.argmax(search_mags_full)
    second_freq_full = search_freqs_full[second_peak_idx_full]
    second_mag_full = search_mags_full[second_peak_idx_full]

    # 将结果存储在main_freqs和main_mags中
    main_freqs = np.array([first_freq_full, second_freq_full])
    main_mags = np.array([first_mag_full, second_mag_full])

    print("\n检测到的主要频率成分(基于全部数据):")
    print("频率(Hz)    幅值    频率比")
    print("-" * 40)
    print(f"{main_freqs[0]:8.2f}  {main_mags[0]:8.4e}  (基准频率)")
    print(f"{main_freqs[1]:8.2f}  {main_mags[1]:8.4e}  {main_freqs[1] / main_freqs[0]:8.3f}")
else:
    print(f"\n警告：在预期频率 {expected_second_freq_full:.2f}Hz ±{search_width}Hz 范围内未找到第二个峰值")
    print("将只分析第一个峰值")
    main_freqs = np.array([first_freq_full])
    main_mags = np.array([first_mag_full])

# =========================================
# 设置8-9ms时间选择范围（用于小波变换分析）
# =========================================
print("\n设置8-9ms时间范围用于小波变换分析...")
time_mask = (t >= 8e-3) & (t <= 9e-3)
t_selected = t[time_mask]
x_selected = x[time_mask]

print(f"选定范围内的数据点数: {len(t_selected)}")
print(f"选定范围内的时间范围: {t_selected[0] * 1000:.3f}ms 到 {t_selected[-1] * 1000:.3f}ms")
selected_max_value = np.max(np.abs(x_selected))
print(f"8-9ms区间信号最大振幅(原始): {selected_max_value:.4e}")

# 直接使用全部数据的FFT分析结果
print("\n使用全部数据的FFT分析结果进行归一化...")
print(f"第一个频率: {main_freqs[0]:.2f}Hz, 振幅: {main_mags[0]:.4e}")
if len(main_freqs) > 1:
    print(f"第二个频率: {main_freqs[1]:.2f}Hz, 振幅: {main_mags[1]:.4e}")

# 计算FFT计算时间（8-9ms部分不再计算）
fft_compute_time_8to9 = 0  # 不再进行8-9ms范围的FFT计算

# =========================================
# 使用小波变换分析所有检测到的频率成分（应用到全部信号）
# =========================================
print(f"\n开始小波变换分析所有检测到的频率成分...")
print(f"使用各频率分量自己的全数据FFT最大幅值进行归一化")

# 记录小波变换开始时间
pywt_start_time = time.time()

# 使用复Morlet小波
wavelet = 'cmor3.0-1.0'
central_freq = pywt.central_frequency(wavelet)
print(f"小波 {wavelet} 的中心频率: {central_freq}")

# 为每个主要频率计算对应的尺度
scales = []
for freq in main_freqs:
    scale = (central_freq * fs) / freq
    scales.append(scale)
    print(f"目标频率 {freq:.2f}Hz 对应的尺度: {scale:.4f}")

# 设置边缘排除比例 - 在信号两端各排除这个比例的数据
edge_exclude_ratio = 0.1  # 排除信号两端各10%的数据

# 对每个主要频率进行小波变换
wavelet_results = {}
actual_freqs = []  # 存储实际计算的频率

for i, (freq, scale) in enumerate(zip(main_freqs, scales)):
    # 对完整信号进行小波变换
    wavelet_compute_start = time.time()
    coeffs, frequencies = pywt.cwt(x, [scale], wavelet, sampling_period=1 / fs, method='fft')
    wavelet_compute_end = time.time()
    wavelet_compute_time = wavelet_compute_end - wavelet_compute_start
    pure_compute_time += wavelet_compute_time

    # 获取返回的实际频率值
    actual_freq = frequencies[0]  # 小波变换返回的精确频率值
    actual_freqs.append(actual_freq)

    # 计算完整信号的小波变换结果在8-9ms范围内的部分
    coeffs_8to9ms = coeffs[0][time_mask]

    # 计算中间区域的范围（排除边缘数据）
    data_len = len(coeffs_8to9ms)
    edge_size = int(data_len * edge_exclude_ratio)
    center_start = edge_size
    center_end = data_len - edge_size

    # 只使用中间部分数据计算最大幅值
    center_data = coeffs_8to9ms[center_start:center_end]
    wavelet_amplitude_8to9ms = np.max(np.abs(center_data))

    # 归一化方法：使用各频率分量自己的FFT最大幅值进行归一化
    fft_amplitude = main_mags[i]  # 使用该频率分量的FFT幅值
    norm_factor = fft_amplitude / wavelet_amplitude_8to9ms
    norm_reference = fft_amplitude
    method_name = f"频率{i+1}全数据FFT最大幅值({fft_amplitude:.4e})"

    # 对整个信号应用归一化因子
    wavelet_signal = np.real(coeffs[0]) * norm_factor
    amplitude = np.abs(coeffs[0]) * norm_factor

    # 查看8-9ms区间归一化后的统计信息
    amplitude_8to9ms = amplitude[time_mask]
    max_amp_8to9ms = np.max(amplitude_8to9ms)
    mean_amp_8to9ms = np.mean(amplitude_8to9ms)

    # 计算完整信号的统计信息
    center_data_full = amplitude[
                       int(len(amplitude) * edge_exclude_ratio):int(len(amplitude) * (1 - edge_exclude_ratio))]
    center_max_amp_full = np.max(center_data_full)

    # 存储结果
    wavelet_results[actual_freq] = {
        'signal': wavelet_signal,
        'amplitude': amplitude,
        'target_freq': freq,  # 保存目标频率
        'actual_freq': actual_freq,
        'input_scale': scale,
        'frequencies': frequencies,  # 保存完整的frequencies数组
        'center_range_8to9ms': (center_start, center_end),  # 保存8-9ms中间区域的范围
        'norm_factor': norm_factor,
        'norm_reference': norm_reference,
        'method_name': method_name,
        'amplitude_8to9ms': amplitude_8to9ms,
        'max_amp_8to9ms': max_amp_8to9ms,
        'mean_amp_8to9ms': mean_amp_8to9ms,
        'center_max_amp_full': center_max_amp_full
    }

    print(f"频率分量 {i + 1}:")
    print(f"  目标频率: {freq:.2f}Hz")
    print(f"  实际频率: {actual_freq:.10f}Hz")
    print(f"  频率差异: {actual_freq - freq:.10f}Hz ({(actual_freq - freq) / freq * 100:.10f}%)")
    print(f"  归一化方法: {method_name}")
    print(f"  归一化参考值: {norm_reference:.4e}")
    print(f"  8-9ms区间原始信号最大幅值: {selected_max_value:.4e}")
    print(f"  8-9ms区间小波变换最大幅值: {wavelet_amplitude_8to9ms:.4e}")
    print(f"  使用的数据范围: 排除两端各{edge_exclude_ratio * 100:.0f}%的数据")
    print(f"  归一化因子: {norm_factor:.4e}")
    print(f"  全信号平均幅度: {np.mean(amplitude):.4e}")
    print(f"  全信号最大幅度: {np.max(amplitude):.4e}")
    print(f"  全信号中间区域最大幅度: {center_max_amp_full:.4e}")
    print(f"  8-9ms区间平均幅度: {mean_amp_8to9ms:.4e}")
    print(f"  8-9ms区间最大幅度: {max_amp_8to9ms:.4e}")
    print(f"  归一化后幅度与原始信号比较: {max_amp_8to9ms / selected_max_value:.4f}")

# 保存小波变换的频率和幅值结果
wavelet_freq_amp_file = os.path.join(result_folder, 'wavelet_frequencies_amplitudes.txt')
with open(wavelet_freq_amp_file, 'w', encoding='utf-8') as f:
    f.write(
        "目标频率(Hz) 实际频率(Hz) 频率差异(Hz) 频率差异(%) 全信号平均幅值 全信号最大幅值 全信号中间区域最大 8-9ms平均幅值 8-9ms最大幅值 输入尺度 归一化方法\n")
    f.write("-" * 160 + "\n")
    for actual_freq in actual_freqs:
        result = wavelet_results[actual_freq]
        target_freq = result['target_freq']
        freq_diff = actual_freq - target_freq
        freq_diff_percent = freq_diff / target_freq * 100 if target_freq != 0 else 0
        f.write(
            f"{target_freq:12.2f} {actual_freq:20.10f} {freq_diff:12.10f} {freq_diff_percent:12.10f} {np.mean(result['amplitude']):15.4e} {np.max(result['amplitude']):15.4e} {result['center_max_amp_full']:15.4e} {result['mean_amp_8to9ms']:15.4e} {result['max_amp_8to9ms']:15.4e} {result['input_scale']:10.4f} {result['method_name']}\n")

# 记录小波变换结束时间并计算总时间
pywt_end_time = time.time()
pywt_elapsed_time = pywt_end_time - pywt_start_time
print(f"\n小波变换总耗时: {pywt_elapsed_time:.4f} 秒")
print(f"其中纯计算耗时: {pure_compute_time - fft_compute_time - fft_compute_time_8to9:.4f} 秒")
print(f"其他操作耗时: {pywt_elapsed_time - (pure_compute_time - fft_compute_time - fft_compute_time_8to9):.4f} 秒")

# 打印分析结果
print("\n频率分量分析结果:")
for i, actual_freq in enumerate(actual_freqs):
    result = wavelet_results[actual_freq]
    target_freq = result['target_freq']
    print(f"\n{actual_freq:.0f}Hz 分量 (目标频率 {target_freq:.2f}Hz):")
    print(f"平均幅度: {np.mean(result['amplitude']):.4e}")
    print(f"最大幅度: {np.max(result['amplitude']):.4e}")
    print(f"中间区域最大幅度: {result['center_max_amp_full']:.4e}")
    print(f"8-9ms区间平均幅度: {result['mean_amp_8to9ms']:.4e}")
    print(f"8-9ms区间最大幅度: {result['max_amp_8to9ms']:.4e}")

# =========================================
# 保存分析结果到文件
# =========================================

# 保存主要频率信息
with open(os.path.join(result_folder, 'frequency_info.txt'), 'w', encoding='utf-8') as f:
    f.write("检测到的主要频率(基于全部数据):\n")
    f.write("目标频率(Hz)    实际频率(Hz)        幅值(全部数据)\n")
    f.write("-" * 50 + "\n")
    for i, actual_freq in enumerate(actual_freqs):
        target_freq = wavelet_results[actual_freq]['target_freq']
        f.write(f"{target_freq:8.2f}  {actual_freq:20.10f}  {main_mags[i]:8.4e}\n")
    
# 不再保存8-9ms范围的第二模态信息，直接使用全部数据结果

# 保存原始信号和时间数据
signal_data = np.column_stack((t, x))
np.savetxt(os.path.join(result_folder, 'original_signal.txt'), signal_data,
           fmt='%.8f %.8e',
           delimiter=' ',
           comments='')

# 保存选定时间范围的原始信号和时间数据
signal_data_selected = np.column_stack((t_selected, x_selected))
np.savetxt(os.path.join(result_folder, 'selected_signal_8to9ms.txt'), signal_data_selected,
           fmt='%.8f %.8e',
           delimiter=' ',
           comments='')

# 保存小波变换结果
for i, actual_freq in enumerate(actual_freqs):
    filename = f'wavelet_{actual_freq:.0f}Hz.txt'
    wavelet_data = np.column_stack((
        t,
        wavelet_results[actual_freq]['signal'],
        wavelet_results[actual_freq]['amplitude']
    ))
    target_freq = wavelet_results[actual_freq]['target_freq']
    method_name = wavelet_results[actual_freq]['method_name']
    np.savetxt(os.path.join(result_folder, filename), wavelet_data,
               header=f'Time(s) Signal(target={target_freq:.2f}Hz,actual={actual_freq:.10f}Hz,norm={method_name}) Amplitude',
               fmt='%.8f %.8e %.8e',
               delimiter=' ',
               comments='')

# 计算总运行时间
total_end_time = time.time()
total_elapsed_time = total_end_time - total_start_time
io_time = total_elapsed_time - pure_compute_time

print(f"\n数据已保存到文件夹：{result_folder}")
print("包含以下文件：")
print("1. frequency_info.txt - 主要频率信息(基于全部数据)")
print("2. wavelet_frequencies_amplitudes.txt - 小波变换详细信息")
print("3. fft_results_full_data.txt - 全部数据的FFT分析结果")
print("4. original_signal.txt - 原始信号数据")
print("5. selected_signal_8to9ms.txt - 8-9ms范围的信号数据")
for i, actual_freq in enumerate(actual_freqs):
    result = wavelet_results[actual_freq]
    fft_norm_amp = main_mags[i]
    print(f"{6 + i}. wavelet_{actual_freq:.0f}Hz.txt - {actual_freq:.0f}Hz分量的小波分析结果(FFT归一化: {fft_norm_amp:.4e})")

# 打印性能分析结果
print("\n性能分析结果:")
print(f"总运行时间: {total_elapsed_time:.4f} 秒")
print(f"纯计算时间: {pure_compute_time:.4f} 秒 ({pure_compute_time / total_elapsed_time * 100:.1f}%)")
print(f"其他操作时间: {io_time:.4f} 秒 ({io_time / total_elapsed_time * 100:.1f}%)")
print("\n处理说明:")
print("- 使用全部数据进行FFT分析获取两个主要频率及其最大幅值")
print("- 8-9ms时间范围仅用于小波变换结果的显示和分析")
print("- 使用各频率分量自己的全数据FFT最大幅值进行小波变换归一化")
print("- 第一个频率分量使用第一个频率的全数据FFT幅值归一化")  
print("- 第二个频率分量使用第二个频率的全数据FFT幅值归一化")
print("- FFT频谱已包含×2修正（对非直流和Nyquist频率）")
