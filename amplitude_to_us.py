import numpy as np
import os
from scipy import signal
from scipy.ndimage import uniform_filter1d

def calculate_us(A, k_i, Q_i, epsilon_0, z_a, R, U_AC):
    """
    根据给定的振幅A计算Us（只考虑共振项）
    
    参数:
    A: 振幅
    k_i: k_i参数
    Q_i: Q_i参数
    epsilon_0: ε₀ 真空介电常数
    z_a: z_a参数
    R: R参数
    U_AC: U_AC参数
    
    返回:
    Us: 计算得到的Us值，可以是单个值或数组
    """
    denominator_term = (z_a/R)**2 + z_a/R
    C = k_i / (2 * np.pi * epsilon_0 * Q_i)
    Us = C * A * denominator_term / U_AC
    return Us

def trim_data_edges(times, values, trim_percent=5):
    """
    去掉数据两端的百分比
    
    参数:
    times: 时间数组
    values: 数值数组  
    trim_percent: 要去掉的百分比（默认5%）
    
    返回:
    trimmed_times, trimmed_values: 去掉两端后的数据
    """
    n = len(times)
    trim_count = int(n * trim_percent / 100)
    
    if trim_count * 2 >= n:
        print(f"警告: 去掉两端{trim_percent}%的数据会移除所有数据点，保持原数据")
        return times, values
    
    start_idx = trim_count
    end_idx = n - trim_count
    
    print(f"去掉数据两端{trim_percent}%: 移除前{trim_count}个点和后{trim_count}个点")
    print(f"原始数据点数: {n}, 处理后数据点数: {end_idx - start_idx}")
    
    return times[start_idx:end_idx], values[start_idx:end_idx]

def detect_steep_change(times, values, threshold_ratio=0.3, window_size=50):
    """
    检测信号中的急剧变化点
    
    参数:
    times: 时间数组
    values: 数值数组
    threshold_ratio: 变化幅度阈值比率（相对于总变化范围）
    window_size: 滑动窗口大小
    
    返回:
    change_points: 变化点的索引列表
    """
    # 计算一阶差分
    diff = np.diff(values)
    
    # 使用滑动窗口计算局部标准差
    if len(diff) < window_size:
        window_size = len(diff) // 2
    
    if window_size < 1:
        window_size = 1
        
    # 计算滑动标准差
    padded_diff = np.pad(diff, window_size//2, mode='edge')
    local_std = uniform_filter1d(np.abs(padded_diff), size=window_size)[window_size//2:window_size//2+len(diff)]
    
    # 计算全局阈值
    total_range = np.max(values) - np.min(values)
    threshold = threshold_ratio * total_range
    
    # 找到超过阈值的变化点
    change_points = []
    for i in range(len(diff)):
        if abs(diff[i]) > threshold and local_std[i] > np.std(diff) * 2:
            change_points.append(i)
    
    print(f"检测到{len(change_points)}个急剧变化点")
    if change_points:
        for i, cp in enumerate(change_points[:5]):  # 只显示前5个
            print(f"  变化点{i+1}: 索引{cp}, 时间{times[cp]:.6e}s, 变化量{diff[cp]:.6e}")
    
    return change_points

def detect_stable_regions(times, values, stability_threshold=0.05, min_region_length=100):
    """
    检测信号中的稳定区间
    
    参数:
    times: 时间数组
    values: 数值数组
    stability_threshold: 稳定性阈值（相对标准差）
    min_region_length: 最小区间长度
    
    返回:
    stable_regions: [(start_idx, end_idx), ...] 稳定区间索引列表
    """
    # 计算移动窗口的标准差
    window_size = min(len(values) // 10, 200)  # 窗口大小为数据长度的1/10，最大200个点
    window_size = max(window_size, 10)  # 最小10个点
    
    # 计算局部变异系数 (CV = std/mean)
    local_cv = []
    for i in range(len(values) - window_size + 1):
        window_data = values[i:i+window_size]
        mean_val = np.mean(window_data)
        std_val = np.std(window_data)
        cv = std_val / abs(mean_val) if abs(mean_val) > 1e-10 else float('inf')
        local_cv.append(cv)
    
    local_cv = np.array(local_cv)
    
    # 找到稳定区间（变异系数小于阈值）
    stable_mask = local_cv < stability_threshold
    
    # 找到连续的稳定区间
    stable_regions = []
    start = None
    
    for i in range(len(stable_mask)):
        if stable_mask[i] and start is None:
            start = i
        elif not stable_mask[i] and start is not None:
            # 结束一个稳定区间
            if i - start >= min_region_length:
                stable_regions.append((start, i + window_size - 1))
            start = None
    
    # 处理最后一个区间
    if start is not None and len(stable_mask) - start >= min_region_length:
        stable_regions.append((start, len(values) - 1))
    
    print(f"检测到{len(stable_regions)}个稳定区间:")
    for i, (start, end) in enumerate(stable_regions):
        region_duration = times[end] - times[start]
        region_mean = np.mean(values[start:end+1])
        region_std = np.std(values[start:end+1])
        region_cv = region_std / abs(region_mean) if abs(region_mean) > 1e-10 else float('inf')
        print(f"  区间{i+1}: 时间{times[start]:.6e}-{times[end]:.6e}s, "
              f"持续时间{region_duration:.6e}s, 均值{region_mean:.6e}, CV={region_cv:.4f}")
    
    return stable_regions

def smooth_regions(times, values, regions, method='mean'):
    """
    对指定区间进行平滑处理
    
    参数:
    times: 时间数组
    values: 数值数组
    regions: [(start_idx, end_idx), ...] 需要平滑的区间
    method: 平滑方法 ('mean', 'median', 'min', 'max')
    
    返回:
    smoothed_values: 平滑后的数值数组
    """
    smoothed_values = values.copy()
    
    print(f"使用{method}方法对{len(regions)}个区间进行平滑处理:")
    
    for i, (start, end) in enumerate(regions):
        region_values = values[start:end+1]
        
        if method == 'mean':
            replacement_value = np.mean(region_values)
        elif method == 'median':
            replacement_value = np.median(region_values)
        elif method == 'min':
            replacement_value = np.min(region_values)
        elif method == 'max':
            replacement_value = np.max(region_values)
        else:
            print(f"未知的平滑方法: {method}, 使用均值")
            replacement_value = np.mean(region_values)
        
        smoothed_values[start:end+1] = replacement_value
        
        region_duration = times[end] - times[start]
        original_range = np.max(region_values) - np.min(region_values)
        print(f"  区间{i+1}: 时间{times[start]:.6e}-{times[end]:.6e}s, "
              f"替换值{replacement_value:.6e}, 原始范围{original_range:.6e}")
    
    return smoothed_values

def smooth_by_average(times, values, points_per_group=10):
    """
    对数据每N个点取平均值进行平滑处理
    
    参数:
    times: 时间数组
    values: 数值数组
    points_per_group: 每组的点数（默认10个点）
    
    返回:
    smoothed_times: 平滑后的时间数组
    smoothed_values: 平滑后的数值数组
    """
    n = len(values)
    n_groups = n // points_per_group
    remainder = n % points_per_group
    
    print(f"数据平滑处理:")
    print(f"  原始数据点数: {n}")
    print(f"  每组点数: {points_per_group}")
    print(f"  完整组数: {n_groups}")
    print(f"  剩余点数: {remainder}")
    
    smoothed_times = []
    smoothed_values = []
    
    # 处理完整的组
    for i in range(n_groups):
        start_idx = i * points_per_group
        end_idx = (i + 1) * points_per_group
        
        # 取每组的时间中点和数值平均值
        group_time_avg = np.mean(times[start_idx:end_idx])
        group_value_avg = np.mean(values[start_idx:end_idx])
        
        smoothed_times.append(group_time_avg)
        smoothed_values.append(group_value_avg)
    
    # 处理剩余的点
    if remainder > 0:
        start_idx = n_groups * points_per_group
        group_time_avg = np.mean(times[start_idx:])
        group_value_avg = np.mean(values[start_idx:])
        
        smoothed_times.append(group_time_avg)
        smoothed_values.append(group_value_avg)
        print(f"  处理剩余{remainder}个点作为最后一组")
    
    smoothed_times = np.array(smoothed_times)
    smoothed_values = np.array(smoothed_values)
    
    print(f"  平滑后数据点数: {len(smoothed_values)}")
    print(f"  数据压缩比: {len(smoothed_values)/n:.3f}")
    
    return smoothed_times, smoothed_values

def importdata(file_path, delimiter=None, skip_header=0):
    """
    读取数据文件
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    with open(file_path, 'r') as file:
        lines = file.readlines()
    
    header_lines = lines[:skip_header]
    data_lines = lines[skip_header:]
    
    first_data_line = data_lines[0].strip()
    is_header = False
    
    parts = first_data_line.split(delimiter) if delimiter else first_data_line.split()
    try:
        [float(p) for p in parts]
    except ValueError:
        is_header = True
    
    if is_header:
        data_lines = data_lines[1:]
        header_lines.append(first_data_line)
    
    data = []
    for line in data_lines:
        line = line.strip()
        if not line:
            continue
            
        parts = line.split(delimiter) if delimiter else line.split()
        try:
            values = [float(p) for p in parts]
            data.append(values)
        except ValueError as e:
            print(f"跳过无法解析的行: {line}, 错误: {e}")
    
    return np.array(data) if data else np.array([])

def read_amplitude_data(file_path, time_column=0, amplitude_column=2):
    """
    从文本文件读取时间和振幅数据
    """
    try:
        data = importdata(file_path)
        
        if data.size == 0:
            print(f"警告: 文件 {file_path} 中没有有效数据")
            return np.array([]), np.array([])
            
        if data.shape[1] <= max(time_column, amplitude_column):
            raise ValueError(f"数据列数不足: 需要列 {time_column} 和 {amplitude_column}，但只有 {data.shape[1]} 列")
            
        times = data[:, time_column]
        amplitudes = data[:, amplitude_column]
        
        return times, amplitudes
        
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
        return np.array([]), np.array([])

def detect_waves_by_period(signal, times, period_seconds):
    """
    基于用户设置的周期来分割每个波并计算最小值
    
    参数:
    signal: 信号数组
    times: 时间数组
    period_seconds: 用户设置的周期（秒）
    
    返回:
    wave_segments: 每个波的数据段列表
    """
    dt = times[1] - times[0]  # 时间步长
    period_samples = int(period_seconds / dt)  # 每个周期的采样点数
    
    print(f"周期分割信息：")
    print(f"  设置的周期: {period_seconds:.6e} 秒")
    print(f"  时间步长: {dt:.6e} 秒")
    print(f"  每周期采样点数: {period_samples}")
    print(f"  信号总长度: {len(signal)} 个点")
    
    # 计算完整周期数
    total_samples = len(signal)
    complete_periods = total_samples // period_samples
    remaining_samples = total_samples % period_samples
    
    print(f"  完整周期数: {complete_periods}")
    print(f"  剩余采样点数: {remaining_samples}")
    
    wave_segments = []
    
    # 处理完整周期
    for period_idx in range(complete_periods):
        start_idx = period_idx * period_samples
        end_idx = (period_idx + 1) * period_samples - 1
        
        segment = {
            'start_idx': start_idx,
            'end_idx': end_idx,
            'times': times[start_idx:end_idx+1],
            'values': signal[start_idx:end_idx+1]
        }
        wave_segments.append(segment)
    
    # 处理剩余的不完整周期（如果存在）
    if remaining_samples > 0:
        start_idx = complete_periods * period_samples
        end_idx = total_samples - 1
        
        segment = {
            'start_idx': start_idx,
            'end_idx': end_idx,
            'times': times[start_idx:end_idx+1],
            'values': signal[start_idx:end_idx+1]
        }
        wave_segments.append(segment)
        print(f"  添加不完整周期: {remaining_samples} 个采样点")
    
    print(f"共分割出 {len(wave_segments)} 个周期")
    
    return wave_segments

if __name__ == "__main__":
    # 参数设置
    k_i = 7.86  # 悬臂刚度
    Q_i = 250.8  # 悬臂品质因子
    epsilon_0 = 8.854e-12  # 真空介电常数
    z_a = 120e-9  # 探针抬升高度              # 注意修改！！！
    R = 20e-9  # 探针针尖半径
    U_AC = 3  # 交流激励电压的幅值
    
    # 数据处理参数
    trim_percent = 0  # 去掉两端的百分比
    points_per_group = 2000  # 每组点数，用于平滑处理
    
    # 设置数据文件夹
    data_folder = "data02"
    
    # 设置结果根路径变量（这样保证数据从同一个文件夹读取并写入到同一个文件夹）
    result_root = "result_cwtnew"
    
    # 设置文件路径
    data_file = f"{result_root}/{data_folder}/wavelet_470285Hz.txt"
    times, amplitudes = read_amplitude_data(data_file)

    if len(times) > 0:
        print(f"读取到 {len(times)} 个数据点")
        print(f"振幅范围: {np.min(amplitudes):.6e} 到 {np.max(amplitudes):.6e}")
        
        # 步骤1: 去掉两端5%的数据
        print(f"\n步骤1: 去掉数据两端{trim_percent}%...")
        times_trimmed, amplitudes_trimmed = trim_data_edges(times, amplitudes, trim_percent)
        
        # 计算对应的Us值
        print("\n步骤2: 使用公式计算Us值...")
        Us_values_raw = calculate_us(amplitudes_trimmed, k_i, Q_i, epsilon_0, z_a, R, U_AC)
        
        # 保存原始电压数据（去掉两端后，未平滑）
        voltage_data_raw = np.column_stack((times_trimmed, Us_values_raw))
        output_file_raw = f"{result_root}/{data_folder}/us_vs_time.txt"
        np.savetxt(output_file_raw, voltage_data_raw, fmt='%.8e', 
                  header='Time(s) Us_Original(V)', comments='')
        print(f"原始电压数据已保存到: {output_file_raw}")
        
        # 步骤3: 每组取平均值平滑处理
        print(f"\n步骤3: 每{points_per_group}个点取平均值平滑处理...")
        times_smoothed, Us_values_smoothed = smooth_by_average(
            times_trimmed, Us_values_raw, points_per_group
        )
        
        # 保存平滑后的电压数据
        voltage_data_smoothed = np.column_stack((times_smoothed, Us_values_smoothed))
        output_file_smoothed = f"{result_root}/{data_folder}/us_vs_time_smoothed.txt"
        np.savetxt(output_file_smoothed, voltage_data_smoothed, fmt='%.8e', 
                  header='Time(s) Us_Smoothed(V)', comments='')
        print(f"平滑后电压数据已保存到: {output_file_smoothed}")
        
        # 输出统计信息
        print(f"\n处理统计信息:")
        print(f"原始数据点数: {len(times)}")
        print(f"去掉两端后数据点数: {len(times_trimmed)}")
        print(f"平滑后数据点数: {len(times_smoothed)}")
        print(f"原始时间范围: {times_trimmed[0]:.6e} 到 {times_trimmed[-1]:.6e} s")
        print(f"平滑后时间范围: {times_smoothed[0]:.6e} 到 {times_smoothed[-1]:.6e} s")
        print(f"原始Us范围: {np.min(Us_values_raw):.6e} 到 {np.max(Us_values_raw):.6e} V")
        print(f"平滑后Us范围: {np.min(Us_values_smoothed):.6e} 到 {np.max(Us_values_smoothed):.6e} V")
        print(f"平滑方法: 每{points_per_group}个点取平均值")

        print(f"数据处理: 去掉两端{trim_percent}%, 每{points_per_group}个点取平均值")
        
    else:
        print("无法处理数据，请检查输入文件") 