% 保存yout数据，并重命名为yout0
yout9 = yout; % 确保 yout 变量已定义  % 每次都需要改 4
save('yout9.mat', 'yout9');  % 每次都需要改 3

%yout2.mat  2-1-2
%yout3.mat  2-1-1
%yout5.mat  2-1-2-1 周期1ms
%yout6.mat  2-1-2-1 周期2ms
%yout7.mat  2-1-2-1 周期2ms
%yout8.mat  2-1-2-1 周期2ms 采样率2M

%yout9.mat  2-1-2 专利 采样率100M

% 设定采样率
Fs = 1e8; % 采样率 100,000,000 Hz    % 注意改采样率

% 设定时间区间
start_time = 10e-3; % 10毫秒
end_time = 18e-3; % 18毫秒

% 计算对应的采样点索引
start_index = round(start_time * Fs);
end_index = round(end_time * Fs);

% 确保索引在信号范围内
if end_index > length(yout)
    end_index = length(yout);
end

% 提取数据
extracted_data9 = yout(start_index:end_index); % 每次都需要改 2

% 保存提取后的数据
save('extracted_data9.mat', 'extracted_data9'); % 保存提取的数据 % 每次都需要改 1

% 绘制提取的数据
figure;
plot((start_index:end_index) / Fs, extracted_data9); % 每次都需要改 0
xlabel('时间 (秒)');
ylabel('信号幅度');
title('提取的信号数据 (9到13毫秒)');
grid on;




