% % 加载数据
% load('extracted_data11.mat');  % 每次都需要改 2
% 
% % 设置采样频率
% Fs = 1e8; % 采样频率 (Hz) % 注意采样率
% 
% % 使用 Morlet 小波进行连续小波变换
% [cfs, freq] = cwt(extracted_data11, Fs);  % 每次都需要改 1
% 
% % 计算振幅
% amplitude = abs(cfs);
% 
% % 第一个频率范围
% minFreq1 = 70000; % 最小频率 (Hz)
% maxFreq1 = 74000; % 最大频率 (Hz)
% 
% % 第二个频率范围
% minFreq2 = 410000; % 最小频率 (Hz)
% maxFreq2 = 440000; % 最大频率 (Hz)
% 
% % 找到第一个频率范围对应的索引
% freqIndex1 = freq >= minFreq1 & freq <= maxFreq1;
% 
% % 找到第二个频率范围对应的索引
% freqIndex2 = freq >= minFreq2 & freq <= maxFreq2;
% 
% % 选择时间段（可选）
% startTime = 0; % 起始时间 (秒)
% endTime = 0.1; % 结束时间 (秒)
% 
% % 找到时间段对应的索引
% N = length(extracted_data11);  % 每次都需要改 0 
% dt = 1 / Fs; % 采样间隔
% time = (0:N-1) * dt; % 时间向量
% startIndex = find(time >= startTime, 1);
% endIndex = find(time <= endTime, 1, 'last');
% 
% % 选择对应的时间和振幅数据
% selectedAmplitude1 = amplitude(freqIndex1, startIndex:endIndex); % 第一个频率范围的振幅
% selectedAmplitude2 = amplitude(freqIndex2, startIndex:endIndex); % 第二个频率范围的振幅
% selectedTime = time(startIndex:endIndex); % 时间数据
% 
% % 生成第一张图片：频率范围 65kHz - 80kHz
% figure;
% plot(selectedTime, selectedAmplitude1);
% xlabel('时间 (s)');
% ylabel('振幅');
% title('第一模态振幅随时间变化');
% 
% % 生成第二张图片：频率范围 460kHz - 480kHz
% figure;
% plot(selectedTime, selectedAmplitude2);
% xlabel('时间 (s)');
% ylabel('振幅');
% title('第二模态振幅随时间变化');

% 加载数据
load('extracted_data11.mat');  % 每次都需要改 2

% 设置采样频率
Fs = 1e8; % 采样频率 (Hz)

% 开始计时
tic;

% 使用 Morlet 小波进行连续小波变换
[cfs, freq] = cwt(extracted_data11, Fs);  % 每次都需要改 1

% 结束计时
elapsedTime = toc;

% 计算振幅谱
amplitude = abs(cfs);

% 对时间轴方向求平均，得到每个频率的平均振幅
amplitude_mean = mean(amplitude, 2);  % 结果是 freq x 1

% 绘制频率-振幅的散点图，展示频率离散性
figure;
stem(freq, amplitude_mean, 'filled', 'MarkerSize', 3);
xlabel('Frequency (Hz)');
ylabel('Average Amplitude');
title('小波变换幅频图');
grid on;
set(gca, 'XScale', 'log');  % 如果频率范围很广，建议使用对数坐标

% 输出运行时间
fprintf('脚本运行时间: %.4f 秒\n', elapsedTime);
