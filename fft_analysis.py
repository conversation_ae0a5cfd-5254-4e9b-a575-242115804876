import numpy as np
import matplotlib.pyplot as plt
import os
import matplotlib

# 配置matplotlib支持中文显示
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'SimSun', 'FangSong']
matplotlib.rcParams['axes.unicode_minus'] = False


def read_time_series_data(file_path):
    """
    读取时间序列数据
    
    参数:
    file_path: 数据文件路径
    
    返回:
    times: 时间数组
    values: 数值数组
    """
    try:
        print(f"正在读取文件: {file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 读取数据，跳过标题行
        data = np.loadtxt(file_path, skiprows=1)
        
        if data.ndim == 1:
            # 如果只有一列数据，假设是数值，时间从0开始
            values = data
            times = np.arange(len(values))
            print(f"读取到单列数据，数据点数: {len(values)}")
        else:
            # 如果有两列或更多，假设第一列是时间，第二列是数值
            times = data[:, 0]
            values = data[:, 1]
            print(f"读取到时间序列数据，数据点数: {len(times)}")
        
        print(f"时间范围: {times[0]:.6e} 到 {times[-1]:.6e} s")
        print(f"数值范围: {np.min(values):.6e} 到 {np.max(values):.6e}")
        
        return times, values
        
    except Exception as e:
        print(f"读取文件出错: {e}")
        return np.array([]), np.array([])

def perform_fft_analysis(times, values):
    """
    对时间序列数据进行FFT分析
    
    参数:
    times: 时间数组
    values: 数值数组
    
    返回:
    frequencies: 频率数组
    magnitudes: 幅值数组
    phases: 相位数组
    fft_result: 复数FFT结果
    """
    # 计算采样参数
    N = len(values)
    dt = times[1] - times[0] if len(times) > 1 else 1.0  # 采样时间间隔
    fs = 1.0 / dt  # 采样频率
    
    print(f"\nFFT分析参数:")
    print(f"  数据点数: {N}")
    print(f"  采样时间间隔: {dt:.6e} s")
    print(f"  采样频率: {fs:.2e} Hz")
    print(f"  频率分辨率: {fs/N:.2e} Hz")
    print(f"  最大分析频率: {fs/2:.2e} Hz")
    
    # 进行FFT变换
    fft_result = np.fft.fft(values)
    
    # 计算频率轴（只取正频率部分）
    frequencies = np.fft.fftfreq(N, dt)[:N//2]
    
    # 计算幅值（只取正频率部分，并进行归一化）
    magnitudes = np.abs(fft_result[:N//2]) * 2.0 / N
    magnitudes[0] /= 2.0  # DC分量不需要乘以2
    
    # 计算相位（只取正频率部分）
    phases = np.angle(fft_result[:N//2])
    
    return frequencies, magnitudes, phases, fft_result

def plot_fft_results(times, values, frequencies, magnitudes, phases, output_folder):
    """
    绘制FFT分析结果
    
    参数:
    times: 时间数组
    values: 原始数值数组
    frequencies: 频率数组
    magnitudes: 幅值数组
    phases: 相位数组
    output_folder: 输出文件夹
    """
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # 1. 原始时域信号
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(times, values, 'b-', linewidth=0.5)
    plt.xlabel('时间 (s)')
    plt.ylabel('幅值')
    plt.title('原始时域信号')
    plt.grid(True)
    
    # 2. 幅频特性（线性坐标）
    plt.subplot(2, 2, 2)
    plt.plot(frequencies, magnitudes, 'r-', linewidth=0.8)
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅值')
    plt.title('幅频特性 (线性坐标)')
    plt.grid(True)
    
    # 3. 幅频特性（对数坐标）
    plt.subplot(2, 2, 3)
    plt.semilogy(frequencies, magnitudes, 'r-', linewidth=0.8)
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅值 (对数坐标)')
    plt.title('幅频特性 (对数坐标)')
    plt.grid(True)
    
    # 4. 相频特性
    plt.subplot(2, 2, 4)
    plt.plot(frequencies, phases * 180 / np.pi, 'g-', linewidth=0.8)
    plt.xlabel('频率 (Hz)')
    plt.ylabel('相位 (度)')
    plt.title('相频特性')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, 'fft_analysis_overview.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 单独绘制详细的幅频特性图
    plt.figure(figsize=(12, 6))
    
    # 线性坐标
    plt.subplot(1, 2, 1)
    plt.plot(frequencies, magnitudes, 'r-', linewidth=0.8)
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅值')
    plt.title('幅频特性 (线性坐标)')
    plt.grid(True)
    
    # 对数坐标
    plt.subplot(1, 2, 2)
    plt.loglog(frequencies[1:], magnitudes[1:], 'r-', linewidth=0.8)  # 跳过DC分量
    plt.xlabel('频率 (Hz) - 对数坐标')
    plt.ylabel('幅值 - 对数坐标')
    plt.title('幅频特性 (双对数坐标)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, 'magnitude_frequency_detailed.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"图像已保存到: {output_folder}")

def find_dominant_frequencies(frequencies, magnitudes, num_peaks=10):
    """
    找出主要的频率分量
    
    参数:
    frequencies: 频率数组
    magnitudes: 幅值数组
    num_peaks: 要显示的峰值数量
    
    返回:
    dominant_freqs: 主要频率
    dominant_mags: 对应的幅值
    """
    # 跳过DC分量（频率为0的分量）
    start_idx = 1 if frequencies[0] == 0 else 0
    
    # 找到最大的几个峰值
    sorted_indices = np.argsort(magnitudes[start_idx:])[::-1]
    sorted_indices += start_idx  # 调整索引
    
    # 取前num_peaks个
    top_indices = sorted_indices[:num_peaks]
    
    dominant_freqs = frequencies[top_indices]
    dominant_mags = magnitudes[top_indices]
    
    return dominant_freqs, dominant_mags

def save_fft_results(frequencies, magnitudes, phases, output_folder):
    """
    保存FFT分析结果到文件
    
    参数:
    frequencies: 频率数组
    magnitudes: 幅值数组
    phases: 相位数组
    output_folder: 输出文件夹
    """
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # 保存完整的FFT结果
    fft_data = np.column_stack((frequencies, magnitudes, phases))
    header = "Frequency(Hz) Magnitude Phase(rad)"
    
    output_file = os.path.join(output_folder, 'fft_results.txt')
    np.savetxt(output_file, fft_data, header=header, fmt='%.6e')
    print(f"FFT结果已保存到: {output_file}")
    
    # 找出并保存主要频率分量
    dominant_freqs, dominant_mags = find_dominant_frequencies(frequencies, magnitudes, 20)
    
    print(f"\n主要频率分量 (前20个):")
    print("频率 (Hz)        幅值")
    print("-" * 30)
    for i, (freq, mag) in enumerate(zip(dominant_freqs, dominant_mags)):
        print(f"{freq:12.2f}    {mag:.6e}")
    
    # 保存主要频率分量
    dominant_data = np.column_stack((dominant_freqs, dominant_mags))
    dominant_file = os.path.join(output_folder, 'dominant_frequencies.txt')
    np.savetxt(dominant_file, dominant_data, 
               header="Frequency(Hz) Magnitude", fmt='%.6e')
    print(f"主要频率分量已保存到: {dominant_file}")

def main():
    """
    主函数
    """
    # 设置参数
    data_folder = 'data12'  # 设置数据文件夹名称，方便更换不同数据
    output_folder = f'vibration_fft_analysis_{data_folder}'  # 输出文件夹
    
    # 设置振动数据文件路径 - 469995Hz频率的振动数据
    data_file = f'result_cwtanalyze_dynamic_all/{data_folder}/wavelet_470285Hz.txt'
    
    print(f"振动数据FFT分析设置:")
    print(f"  数据文件夹: {data_folder}")
    print(f"  振动数据文件: {data_file}")
    print(f"  输出文件夹: {output_folder}")
    print(f"  分析频率: 469995 Hz 的振动数据")
    
    # 读取数据
    times, values = read_time_series_data(data_file)
    
    if len(times) == 0:
        print("错误: 无法读取数据文件")
        return
    
    # 进行FFT分析
    frequencies, magnitudes, phases, fft_result = perform_fft_analysis(times, values)
    
    # 绘制结果
    plot_fft_results(times, values, frequencies, magnitudes, phases, output_folder)
    
    # 保存结果
    save_fft_results(frequencies, magnitudes, phases, output_folder)
    
    # 输出统计信息
    print(f"\n分析完成!")
    print(f"时间序列长度: {len(times)} 个数据点")
    print(f"分析频率范围: 0 - {np.max(frequencies):.2e} Hz")
    print(f"最大幅值: {np.max(magnitudes):.6e}")
    print(f"最大幅值对应频率: {frequencies[np.argmax(magnitudes)]:.2f} Hz")

if __name__ == "__main__":
    main() 