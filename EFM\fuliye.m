% 傅立叶变换
% 加载数据
% load('extracted_data22.mat');  % 假设Amp.mat中包含信号数据，变量名为 'Amp'
% data = readmatrix('2.txt');
% % 提取数据
% y = data(:, 2);
y=yout1;
% 开始计时
tic;

% 设置采样频率
Fs = 1e8;  % 采样频率 (Hz)
N = length(y);  % 信号长度
t = (0:N-1) / Fs;  % 时间序列

% 对输入信号进行去均值处理
y = y - mean(y);

% 计算傅里叶变换
yout_fft = fft(y);

% 计算对应的频率轴
f = (0:N-1)*(Fs/N);  % 频率序列

% 只取一半频率范围，因为傅里叶变换是对称的
half_N = floor(N/2);
f = f(1:half_N);  % 保留一半的频率

% 规范化处理
% 1. 首先除以N来归一化
yout_fft = yout_fft/N;
% 2. 转换为单边谱，除了DC和Nyquist频率外，其他频率需要乘以2
yout_fft_magnitude = abs(yout_fft(1:half_N));
yout_fft_magnitude(2:end-1) = 2*yout_fft_magnitude(2:end-1);

% 绘制线性幅值与频率的关系图
figure('Name', '傅里叶变换幅频图');
plot(f, yout_fft_magnitude);
title('傅里叶变换幅频图');
xlabel('频率 (Hz)');
ylabel('幅度');
axis tight;
grid on;

% 绘制对数坐标下的幅度谱
figure('Name', '对数幅度谱');
semilogx(f, 20*log10(yout_fft_magnitude));
title('对数幅度谱');
xlabel('频率 (Hz)');
ylabel('幅度 (dB)');
axis tight;
grid on;

% 结束计时
elapsedTime = toc;

% 输出运行时间
fprintf('脚本运行时间: %.2f 秒\n', elapsedTime);